<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="我的礼品" isShadow isPerch> </c-navBar>
        </section>
        <section class="content">
            <scroll-view scroll-y class="prize-list">
                <view class="prize-item flex align-cetner gap-12" v-for="(item, index) in prizeList" :key="index">
                    <view class="left">
                        <u-image :src="item.cover" width="220rpx" height="172rpx" mode="aspectFill"></u-image>
                    </view>
                    <view class="right flex-1 flex flex-col justify-between">
                        <view class="flex align-center justify-between">
                            <text class="mf-font-28 flex-1 u-line-1" style="color: #191919">{{ item.title }}</text>
                            <text class="mf-font-20" style="color: #999999; width: 80rpx" v-if="item.status === 0">待领取</text>
                            <text class="mf-font-20" style="color: #1d7bf7; width: 80rpx" v-if="item.status === 1">已领取</text>
                        </view>
                        <view class="flex align-end justify-between">
                            <view class="flex flex-col gap-8 mf-font-24" style="color: #666666">
                                <text>{{ item.activeName }}</text>
                                <text>{{ item.activeTime }}</text>
                            </view>
                            <view
                                class="btn"
                                style="background: #1d7bf7; border-radius: 24rpx; height: 48rpx; width: 104rpx; text-align: center"
                                @click="$fn.jumpPage('/pages/games/pages/claimYourPrize', true)"
                            >
                                <text class="mf-font-24" style="line-height: 24rpx; color: #fff">领取</text>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            prizeList: [
                {
                    cover: "https://picsum.photos/220/172?random=",
                    title: "苹果16pro max 256GB*1",
                    activeName: "活动名称活动名称",
                    activeTime: "2025-07-29 15:30:00",
                    status: 1, // 1: 已领取 0: 未领取
                },
            ],
        };
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        background: #f7f7f7;
        padding: 24rpx 32rpx;
        .prize-list {
            height: calc(100vh - 230rpx);
            .prize-item {
                padding: 16rpx 14rpx;
                background: #ffffff;
                border-radius: 8rpx;
                margin-bottom: 20rpx;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>
