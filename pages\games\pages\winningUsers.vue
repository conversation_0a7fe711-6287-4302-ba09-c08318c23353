<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="获奖用户" isShadow isBack isPerch> </c-navBar>
        </section>
        <section class="content">
            <scroll-view scroll-y class="prize-list">
                <view class="prize-item flex align-cetner gap-12" v-for="(item, index) in prizeList" :key="index">
                    <view class="left">
                        <u-image :src="item.cover" width="220rpx" height="172rpx" mode="aspectFill"></u-image>
                    </view>
                    <view class="right flex-1 flex flex-col justify-between">
                        <view class="flex align-center justify-between">
                            <text class="mf-font-28 flex-1 u-line-1" style="color: #191919">{{ item.title }}</text>
                        </view>
                        <view class="flex align-end justify-between">
                            <view class="flex flex-col gap-8 mf-font-24" style="color: #666666">
                                <text>获奖用户:{{ item.winningUser }}</text>
                                <text>获奖时间:{{ item.winningTime }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            prizeList: [
                {
                    cover: "https://picsum.photos/220/172?random=",
                    title: "苹果16pro max 256GB*1",
                    winningUser: "张三三",
                    winningTime: "2025-07-29 15:30:00",
                },
            ],
        };
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        background: #f7f7f7;
        padding: 24rpx 32rpx;
        .prize-list {
            height: calc(100vh - 230rpx);
            .prize-item {
                padding: 16rpx 14rpx;
                background: #ffffff;
                border-radius: 8rpx;
                margin-bottom: 20rpx;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>
