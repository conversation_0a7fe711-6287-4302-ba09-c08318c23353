<template>
    <view class="tabbar-container">
        <!-- 自定义 tabbar -->
        <view class="custom-tabbar">
            <!-- 普通 tabbar 项 -->
            <view
                v-for="(item, index) in tabbarList"
                :key="index"
                class="tabbar-item"
                :class="{ active: selected === index, 'mid-item': item.midButton }"
                @click="handleTabClick(item)"
            >
                <!-- 中间突出按钮 -->
                <view v-if="item.midButton" class="mid-button-wrapper">
                    <view class="mid-button-bg">
                        <image class="mid-button-icon" :src="selected === index ? item.selectedIconPath : item.iconPath" mode="aspectFit" />
                    </view>
                </view>
                <!-- 普通按钮 -->
                <template v-else>
                    <image class="tabbar-icon" :src="selected === index ? item.selectedIconPath : item.iconPath" mode="aspectFit" />
                    <text class="tabbar-text" :class="{ 'active-text': selected === index }">{{ item.text }}</text>
                </template>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "c-tabbar",
    props: {
        selected: {
            type: Number,
            default: 0,
        },
        midButton: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            tabbarList: [
                {
                    pagePath: "/pages/tabbar/home",
                    text: "首页",
                    iconPath: "/static/tabbar/tabbar1.png",
                    selectedIconPath: "/static/tabbar/tabbar1-a.png",
                },
                {
                    pagePath: "/pages/tabbar/game",
                    text: "游戏",
                    iconPath: "/static/tabbar/tabbar2.png",
                    selectedIconPath: "/static/tabbar/tabbar2-a.png",
                },
                {
                    pagePath: "/pages/tabbar/publish",
                    text: "发布",
                    iconPath: "/static/tabbar/tabbar3.png",
                    selectedIconPath: "/static/tabbar/tabbar3-a.png",
                    midButton: true,
                },
                {
                    pagePath: "/pages/tabbar/message",
                    text: "消息",
                    iconPath: "/static/tabbar/tabbar4.png",
                    selectedIconPath: "/static/tabbar/tabbar4-a.png",
                },
                {
                    pagePath: "/pages/tabbar/mine",
                    text: "我的",
                    iconPath: "/static/tabbar/tabbar5.png",
                    selectedIconPath: "/static/tabbar/tabbar5-a.png",
                },
            ],
        };
    },
    methods: {
        handleTabClick(item) {
            // 发射事件给父组件
            this.$emit("change", item);
            // 普通 tab 切换
            if (item.midButton) {
                this.$fn.jumpPage(item.pagePath);
                return;
            }
            uni.switchTab({
                url: item.pagePath,
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.tabbar-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.custom-tabbar {
    display: flex;
    align-items: flex-end;
    background: #ffffff;
    // border-top: 1px solid #e5e5e5;
    padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
    height: 100rpx;
    position: relative;
    // 添加内部阴影效果，增强层次感
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2rpx;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
        pointer-events: none;
    }
}

.tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;
    position: relative;

    &.mid-item {
        // 中间项不需要额外的 padding
        padding: 0;
    }
}

.tabbar-icon {
    width: 44rpx;
    height: 44rpx;
    margin-bottom: 4rpx;
}

.tabbar-text {
    font-size: 20rpx;
    color: #999999;
    line-height: 1;

    &.active-text {
        color: #1d7bf7;
    }
}

// 中间突出按钮样式
.mid-button-wrapper {
    position: relative;
    top: -5rpx; // 向上突出
    display: flex;
    align-items: center;
    justify-content: center;
}

.mid-button-bg {
    padding: 10rpx;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mid-button-icon {
    width: 100rpx;
    height: 100rpx;
}

// 激活状态
.tabbar-item.active:not(.mid-item) {
    .tabbar-icon {
        transform: scale(1.1);
        transition: transform 0.2s ease;
    }
}
</style>
