const COS = require('./cos-wx-sdk-v5.js')

const tengxunConfig = {
    SecretId: 'AKIDSnvpVxBB2g6KL9i3B9uQimdIjwxkJuKW', // 腾讯云的 SecretId
    SecretKey: '1ssnYGvuofZJrDNJ3auzsa7TfjiWCmCU', // 腾讯云的 SecretKey
    region: 'ap-shanghai', // 腾讯云的 region
    url: 'https://whiteisland-mini-1257762102.cos.ap-shanghai.myqcloud.com', // 腾讯云的 url
    bucketName: 'whiteisland-mini-1257762102' // 腾讯云的 bucketName
}

const Bucket = tengxunConfig.bucketName;//存储桶的名称，命名规则为 BucketName-APPID，此处填写的存储桶名称必须为此格式
const Region = tengxunConfig.region;//存储桶所在地域
//创建一个 COS SDK 实例
// SECRETID 和 SECRETKEY请登录 https://console.cloud.tencent.com/cam/capi 进行查看和管理
const cos = new COS({
    SecretId: tengxunConfig.SecretId,
    SecretKey: tengxunConfig.SecretKey,
});
//创建存储桶
cos.putBucket({
    Bucket: Bucket,
    Region: Region,
}, function (err, data) {
    console.log(err || data);
});

//查询存储桶列表
cos.getService(function (err, data) {
    console.log("[查询存储桶列表]")
    console.log(data && data.Buckets);
});
//删除

function deleteFileToTencentClound(Key) {
    return new Promise((resolve, reject) => {
        cos.deleteObject({
            Bucket: Bucket,
            Region: Region,
            Key: Key,
        }, function (err, data) {
            console.log(err || data);
            resolve(data)
        });
    })
}
//上传图片到腾讯云
function uploadFileToTencentClound(filename, filePath, onProgress) {
    return new Promise((resolve, reject) => {
        cos.postObject({
            Bucket: Bucket,
            Region: Region,
            Key: 'wxFile/' + filename,
            FilePath: filePath,
            onProgress: function (info) {
                console.log("[cos.postObject-seccess]", JSON.stringify(info));
                if (onProgress && typeof onProgress === 'function') {
                    onProgress(info);
                }
            }
        },
            function (err, data) {
                console.log("[cos.postObject-err]", err || data);
                if (err) {
                    reject(err);
                    return;
                }
                resolve(data.headers.location)
            })
    })
}
export default {
    uploadFileToTencentClound,
    deleteFileToTencentClound
}