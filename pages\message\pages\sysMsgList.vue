<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="系统消息" isBack isShadow isPerch></c-navBar>
        </section>
        <section class="content">
            <scroll-view class="msg-list" scroll-y>
                <view class="msg-item" v-for="item in 15" @click="$fn.jumpPage('/pages/message/pages/sysMsgDetail')">
                    <view class="header flex align-center justify-between">
                        <view class="flex align-center gap-8">
                            <u-image src="/pages/message/static/notice.png" width="36rpx" height="36rpx" mode="aspectFill"></u-image>
                            <text class="mf-font-28">系统消息</text>
                        </view>
                        <text class="mf-font-24" style="color: #666">2025-6-25 15:30:28</text>
                    </view>
                    <view class="body flex flex-col gap-12">
                        <text class="mf-font-28" style="color: #191919">这是消息标题这是消息标题</text>
                        <text class="mf-font-28 u-line-2" style="color: #999">
                            这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是这是消息内容这是消息内容这是消息内容这是
                        </text>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        min-height: calc(100vh - 250rpx);
        padding: 40rpx 32rpx;
        background: #f5f5f5;
        .msg-list {
            height: calc(100vh - 250rpx);
            .msg-item {
                border-radius: 12rpx;
                background: #fff;
                margin-bottom: 24rpx;
                &:last-child {
                    margin-bottom: 0;
                }
                .header {
                    padding: 20rpx;
                    border-bottom: 2rpx solid #e6e6e6;
                }
                .body {
                    padding: 20rpx;
                }
            }
        }
    }
}
</style>
