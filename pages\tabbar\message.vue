<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="消息" isShadow isPerch></c-navBar>
        </section>
        <section class="content">
            <scroll-view class="msg-list" scroll-y>
                <view class="msg-item flex align-center gap-24" @click="$fn.jumpPage('/pages/message/pages/sysMsgList')">
                    <view class="left">
                        <u-image src="/static/common/sysmsg.png" width="100rpx" height="100rpx" shape="circle" mode="aspectFill"></u-image>
                    </view>
                    <view class="right flex flex-col flex-1 gap-22">
                        <view class="flex align-center justify-between">
                            <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">系统消息</text>
                            <text class="mf-font-24" style="color: #999999">01-22 22:35</text>
                        </view>
                        <text class="mf-font-24 u-line-1" style="color: ">说真的，你真的非常漂亮，非常符合我的另一半的...</text>
                    </view>
                </view>
                <view class="msg-item flex align-center gap-24" @click="$fn.jumpPage('/pages/message/pages/customerList')">
                    <view class="left">
                        <u-image
                            src="/static/common/customer.png"
                            width="100rpx"
                            height="100rpx"
                            shape="circle"
                            mode="aspectFill"
                        ></u-image>
                    </view>
                    <view class="right flex flex-col flex-1 gap-22">
                        <view class="flex align-center justify-between">
                            <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">客服消息</text>
                            <text class="mf-font-24" style="color: #999999">01-22 22:35</text>
                        </view>
                        <text class="mf-font-24 u-line-1" style="color: ">说真的，你真的非常漂亮，非常符合我的另一半的...</text>
                    </view>
                </view>
                <view class="msg-item flex align-center gap-24" v-for="(item, index) in msgList" :key="index" @click="$fn.jumpPage('/pages/message/pages/sendMsg')">
                    <view class="left">
                        <u-image
                            :src="item.avatar"
                            width="100rpx"
                            height="100rpx"
                            shape="circle"
                            mode="aspectFill"
                        ></u-image>
                    </view>
                    <view class="right flex flex-col flex-1 gap-22">
                        <view class="flex align-center justify-between">
                            <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.title }}</text>
                            <text class="mf-font-24" style="color: #999999">{{ item.time }}</text>
                        </view>
                        <text class="mf-font-24 u-line-1" style="color: 999999">{{ item.content }}</text>
                    </view>
                </view>
            </scroll-view>
        </section>
        <c-tabbar :selected="currentTab" @change="handleTabChange"></c-tabbar>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 3,
            msgList: [],
        };
    },
    onShow() {
        uni.hideTabBar();
    },
    onLoad(options) {
        this.handleGetMsgList();
    },
    methods: {
        async handleGetMsgList() {
            try {
                // TODO
                // 模拟15条消息数据
                const mockData = [];
                for (let i = 0; i < 15; i++) {
                    mockData.push({
                        id: i + 1,
                        title: "张三三",
                        content: "说真的，你真的非常漂亮，非常符合我的另一半的...",
                        time: "01-22 22:35",
                        avatar: `https://picsum.photos/100/100?random=${i}`,
                    });
                }
                this.msgList = mockData;
            } catch (error) {}
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 0 32rpx;
        .msg-list {
            height: calc(100vh - 400rpx);
            .msg-item {
                .right {
                    padding: 24rpx 0;
                    border-bottom: 2rpx solid #e6e6e6;
                }

                &:last-child .right {
                    border-bottom: 0;
                }
            }
        }
    }
}
</style>
