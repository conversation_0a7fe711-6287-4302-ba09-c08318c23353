<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="帖子详情" isPerch isBack isShadow></c-navBar>
        </section>
        <section class="content">
            <view clsss="swiper" v-if="type !== 'shop'">
                <u-swiper
                    height="750rpx"
                    indicator
                    indicatorMode="dot"
                    circular
                    :radius="0"
                    :list="list"
                    mode="number"
                    :effect3d="true"
                    @click="handleSwiperClick"
                ></u-swiper>
            </view>
            <view class="coupon" v-else>
                <u-image src="/pages/posts/static/coupon.png" width="100%" height="186rpx" mode="aspectFill"></u-image>
                <view class="coupon-info flex align-center justify-between">
                    <view class="left flex flex-col gap-12">
                        <text class="mf-font-44" style="color: #ffffff">满168减30</text>
                        <text class="mf-font-24" style="color: #fcf3e3">有效期:2025.08.25</text>
                        <text class="mf-font-24" style="color: #fcf3e3">剩余998张</text>
                    </view>
                    <view class="right flex flex-col justify-center algin-center mf-font-24" style="text-align: center; color: #fff">
                        <text>满168</text>
                        <text>减30</text>
                    </view>
                </view>
            </view>
            <view class="user-info flex align-center justify-between" @click="handleToUserDetail">
                <view class="flex algin-center gap-24">
                    <u-image
                        src="https://picsum.photos/76/76?random=1"
                        radius="8rpx"
                        width="76rpx"
                        height="76rpx"
                        mode="aspectFill"
                    ></u-image>
                    <view class="flex flex-col gap-6">
                        <view class="tag mf-font-20">商家</view>
                        <text class="mf-font-28" style="color: #191919">韩式炸鸡</text>
                    </view>
                </view>
                <u-icon name="arrow-right" color="#666666" size="28rpx"></u-icon>
            </view>
            <view class="message">
                <text class="mf-font-28" style="color: #666">
                    麓湖生态城套三寻2位合租女室友，要求：女.不能带男朋友回家过夜的，2200元/间
                </text>
                <view class="flex align-cetner justify-between mf-font-24" style="color: #999999; margin-top: 20rpx">
                    <text>4天前发布</text>
                    <text>18浏览</text>
                </view>
                <view class="flex align-center justify-between" style="margin-top: 12rpx">
                    <view class="flex align-center gap-8">
                        <u-icon name="map" size="20rpx" color="#787B80"></u-icon>
                        <text class="mf-font-20" style="color: #787b80">四川省成都市武汉区科创园区街105号</text>
                    </view>
                    <text class="mf-font-20" style="color: #1d7bf7">距你3km</text>
                </view>
            </view>
            <view class="coupon-exp flex flex-col gap-24" v-if="type === 'shop'">
                <text class="mf-font-32" style="color: #333333">优惠券说明</text>
                <u-parse :content="content"></u-parse>
            </view>
        </section>
        <section class="footer">
            <u-button
                type="primary"
                size="large"
                :custom-style="{
                    backgroundColor: '#1D7BF7',
                    borderColor: '#1D7BF7',
                    borderRadius: '44rpx',
                    height: '88rpx',
                }"
                @click="$fn.jumpPage('/pages/repair/quotationConfirmation')"
            >
                <view class="flex align-center gap-12" v-if="type !== 'shop'">
                    <u-icon name="chat-fill" color="#fff" size="32rpx"></u-icon>
                    <text class="mf-font-24" style="color: #fff">私聊</text>
                </view>
                <view class="flex align-center gap-12" v-else>
                    <text class="mf-font-24" style="color: #fff">立即领取</text>
                </view>
            </u-button>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            type: "shop", // 是否商家
            content:
                "单笔订单满168元可用，优惠劵金额直接抵扣，每笔订单仅限使用1张优惠劵，不可叠加其他优惠劵不可兑换现金、不找零、不开发票。 注意事项：若使用优惠劵后发生退款，仅退还实际支付金额，如遇恶意刷劵行为，商家有权取消优惠资格。",
            list: [
                "https://picsum.photos/750/750?random=1",
                "https://picsum.photos/750/750?random=2",
                "https://picsum.photos/750/750?random=3",
                "https://picsum.photos/750/750?random=4",
                "https://picsum.photos/750/750?random=5",
            ],
        };
    },
    methods: {
        // 轮播图点击事件
        handleSwiperClick(index) {
            uni.previewImage({
                urls: this.list,
                current: index,
                success: (result) => {},
                fail: (error) => {},
            });
            console.log("点击了第", index, "张图片");
        },
        // 跳转到用户/商家详情
        handleToUserDetail() {
            if (this.type !== "shop") return;
            this.$fn.jumpPage("/pages/posts/pages/shopDetail");
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        .coupon {
            height: 686rpx;
            height: 186rpx;
            border-radius: 8rpx;
            padding: 32rpx;
            background: #f7f7f7;
            position: relative;
            .coupon-info {
                position: absolute;
                left: 32rpx;
                top: 32rpx;
                bottom: 32rpx;
                right: 32rpx;
                .left {
                    padding: 20rpx 24rpx;
                    text {
                        vertical-align: middle;
                    }
                }
                .right {
                    width: 116rpx;
                    height: 116rpx;
                    padding: 34rpx 40rpx;
                    border-radius: 50%;
                }
            }
        }
        .user-info {
            padding: 32rpx;
            border-bottom: 2rpx solid #e6e6e6;
            .tag {
                width: fit-content;
                color: #fff;
                background: #ff8e0d;
                padding: 3rpx 10rpx;
                border-radius: 4rpx;
                line-height: -1;
            }
        }
        .message {
            padding: 32rpx;
        }
        .coupon-exp {
            padding: 32rpx;
            padding-top: 0;
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 40rpx;
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 999;
    }
}
</style>
