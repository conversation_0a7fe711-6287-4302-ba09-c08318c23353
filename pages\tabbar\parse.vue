<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar :title="title" isShadow isBack isSeat isPerch> </c-navBar>
        </section>
        <section class="cotent">
            <u-parse :content="content"></u-parse>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            title: "规则说明",
            content: "<p>规则说明</p>",
        };
    },
    onLoad(options) {
        this.title = decodeURIComponent(options.title);
    },
    methods: {
        handleGetContent() {
            // 获取内容
        },
    },
};
</script>

<style lang="scss" scoped></style>
