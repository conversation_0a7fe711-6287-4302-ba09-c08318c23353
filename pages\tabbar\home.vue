<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="首页" customLeft isPerch backgroundColor="#fff" color="#000">
                <template #left>
                    <view class="leftbox flex align-center gap-10" @click="show = true">
                        <text class="mf-font-28" style="color: #1a1a1a">{{ currentArea }}</text>
                        <u-icon name="arrow-down-fill" color="#1A1A1A" size="10"></u-icon>
                    </view>
                </template>
            </c-navBar>
        </section>
        <section class="content">
            <view class="search-bar" @click="$fn.jumpPage('/pages/posts/pages/search')">
                <u-search
                    placeholder="请输入用户名称或帖子内容"
                    shape="square"
                    bgColor="#F5F6F7"
                    :actionStyle="{
                        borderRadius: '8rpx',
                        background: '#1D7BF7',
                        color: '#fff',
                        height: '68rpx',
                        lineHeight: '68rpx',
                    }"
                    :showAction="false"
                    disabled
                ></u-search>
            </view>
            <scroll-view v-if="isList" class="list" scroll-y>
                <view class="item" v-for="(item, index) in markers" :key="index">
                    <view class="header flex align-center justify-between">
                        <view class="left flex align-center gap-24">
                            <u-image :src="item.avatar" width="80rpx" height="80rpx" radius="8rpx" mode="aspectFill"></u-image>
                            <view class="flex flex-col gap-16">
                                <view class="flex align-center gap-16">
                                    <text class="mf-font-28 mf-weight-bold" style="color: #191919">{{ item.title }}</text>
                                    <text class="tag mf-font-20" style="color: #fff" v-if="item.tag">{{ item.tag }}</text>
                                </view>
                                <text class="mf-font-20" style="color: #909399">1小时前</text>
                            </view>
                        </view>
                        <view class="right">
                            <view class="btn flex align-center gap-8">
                                <u-image src="/static/common/msg.png" width="24rpx" height="24rpx" mode="aspectFill"></u-image>
                                <text class="mf-font-20" style="color: #fff">私聊</text>
                            </view>
                        </view>
                    </view>
                    <view class="body">
                        <text class="message mf-font-24 u-line-2" style="color: #666">
                            {{ item.description }}
                        </text>
                        <view class="images" v-if="item.images && item.images.length > 0">
                            <u-image
                                v-for="(img, imgIndex) in item.images.slice(0, 3)"
                                :key="imgIndex"
                                :src="img"
                                width="220rpx"
                                height="220rpx"
                                radius="12rpx"
                                mode="aspectFill"
                            ></u-image>
                            <view v-if="item.images.length > 3" class="more flex align-center justify-center">
                                <text class="mf-font-20" style="color: #fff">共{{ item.images.length }}张</text>
                            </view>
                        </view>
                        <view class="location flex align-center justify-between">
                            <view class="flex align-center gap-12">
                                <u-icon name="map" bold size="20rpx" color="#999"></u-icon>
                                <text class="mf-font-20" style="color: #999">{{ item.address }}</text>
                            </view>
                            <text class="mf-font-20" style="color: #1d7bf7">距你3km</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <c-map
                v-else
                :latitude="latitude"
                :longitude="longitude"
                :scale="mapScale"
                :markers="markers"
                :show-location="true"
                @bubble-tap="onBubbleTap"
                @marker-tap="onMarkerTap"
            ></c-map>
        </section>
        <section class="float-btn flex flex-col gap-10">
            <u-image
                v-if="!isList"
                @click="isList = !isList"
                src="/static/common/switchlist.png"
                width="96rpx"
                height="96rpx"
                mode="aspectFill"
            ></u-image>
            <u-image
                v-else
                @click="isList = !isList"
                src="/static/common/switchlist-active.png"
                width="96rpx"
                height="96rpx"
                mode="aspectFill"
            ></u-image>
            <u-image
                v-if="!isList"
                src="/static/common/localtion.png"
                width="96rpx"
                height="96rpx"
                mode="aspectFill"
                @click="getCurrentLocation"
            ></u-image>
        </section>
        <c-tabbar :selected="currentTab" @change="handleTabChange"></c-tabbar>
        <u-picker
            :show="show"
            close-on-click-overlay
            :columns="[columns]"
            @cancel="show = false"
            @close="show = false"
            @change="changeHandler"
            @confirm="confirmHandler"
        >
        </u-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            show: false, // 控制选择器的显示隐藏
            isList: false,
            columns: ["四川一区", "四川二区", "南方大区"],
            currentTab: 0, // 当前选中的 tab
            currentArea: "四川一区",
            latitude: 30.6586, // 纬度 (成都默认坐标)
            longitude: 104.0647, // 经度 (成都默认坐标)
            mapScale: 15, // 地图缩放级别
            markers: [], // 地图标记点
        };
    },
    onShow() {
        uni.hideTabBar();
    },
    onLoad() {
        this.getCurrentLocation();
    },
    methods: {
        // 获取当前定位
        async getCurrentLocation() {
            try {
                // 获取地理位置
                const res = await uni.getLocation();
                if (res.errMsg === "getLocation:fail") {
                    this.isList = true;
                    return;
                }
                console.log("定位成功:", res);
                this.latitude = res.latitude;
                this.longitude = res.longitude;
                // 移动到当前位置
                this.mapCtx = uni.createMapContext("map");
                this.mapCtx.moveToLocation();
                // 定位成功后重新生成气泡
                this.generateBubbles();
            } catch (error) {
                this.isList = true;
                console.error("获取定位失败:", error);
                this.$fn.showToast("定位失败");
            }
        },

        // 生成随机气泡数据
        generateBubbles() {
            console.log("开始生成气泡数据");
            const bubbleData = [
                { title: "韩式炸鸡", description: "满减券大放送满减券大放送...", tag: "商家" },
                { title: "王淑芳", description: "寻找合租室友满减券大放送", tag: null },
                { title: "韩式炸鸡", description: "满减券大放送满减券大放送...", tag: "商家" },
                { title: "红烧肉", description: "满减券大放送满减券大放送...", tag: "商家" },
                { title: "韩式炸鸡", description: "满减券大放送满减券大放送...", tag: "商家" },
            ];

            // 生成气泡数据，包含屏幕坐标
            this.bubbles = bubbleData.map((item, index) => {
                // 在当前位置周围2-3公里范围内随机生成坐标
                const randomLat = this.getRandomCoordinate(this.latitude, 0.02);
                const randomLng = this.getRandomCoordinate(this.longitude, 0.02);

                return {
                    id: index,
                    ...item,
                    avatar: `https://picsum.photos/64/64?random=${index + 1}`,
                    images: [
                        "https://picsum.photos/160/160?random=1",
                        "https://picsum.photos/160/160?random=2",
                        "https://picsum.photos/160/160?random=3",
                        "https://picsum.photos/160/160?random=4",
                        "https://picsum.photos/160/160?random=5",
                    ],
                    latitude: randomLat,
                    longitude: randomLng,
                };
            });

            // 生成隐藏的 markers（用于定位参考）
            this.markers = this.bubbles.map((bubble, index) => ({
                id: index,
                ...bubble,
                width: 20,
                height: 6,
                iconPath: "/static/common/point.png", // 隐藏图标
                address: "四川省成都市高新区天府三街1号",
                customCallout: {
                    anchorX: 0,
                    anchorY: -5,
                    display: "ALWAYS",
                },
            }));
            console.log("生成的 markers 数据:", this.markers);
        },
        // 生成随机坐标（在指定范围内）
        getRandomCoordinate(center, range) {
            return center + (Math.random() - 0.5) * 2 * range;
        },

        // 气泡点击事件
        onBubbleTap(bubble) {
            console.log("首页 - 接收到气泡点击事件:", bubble);
            if (!bubble) {
                console.error("首页 - 气泡数据为空");
                return;
            }
            this.$fn.jumpPage(`/pages/posts/pages/postDetail?makerId=${bubble.detail.markerId}`);
        },

        // 地图标记点击事件
        onMarkerTap(e) {
            console.log("点击标记:", e);
        },
        // 切换 tab
        handleTabChange(index) {
            // this.currentTab = index;
            // console.log("Tab 切换到:", index);
        },
        // 选择地区
        changeHandler(e) {
            this.currentArea = e.value[0];
        },
        // 确认选择
        confirmHandler(e) {
            this.currentArea = e.value[0];
            this.show = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    min-height: calc(100vh - 120rpx);
    padding-bottom: 120rpx; // 为 tabbar 预留空间
    background: #f5f6f7;
    .nav-bar {
        .leftbox {
            margin-left: 12rpx;
        }
    }

    .content {
        .search-bar {
            padding: 22rpx 32rpx;
            background: #fff;
            border-radius: 0 0 16rpx 16rpx;
            overflow: hidden;
            box-shadow: 0rpx 2rpx 12rpx 0rpx rgba(0, 29, 125, 0.1);
        }
        .list {
            height: calc(100vh - 384rpx);
            .item {
                padding: 32rpx;
                margin-bottom: 8rpx;
                background: #fff;
                &:last-child {
                    margin-bottom: 0;
                }
                .header {
                    .left {
                        .tag {
                            background: #ff8e0d;
                            padding: 6rpx 10rpx;
                            border-radius: 4rpx;
                        }
                    }
                    .right {
                        .btn {
                            padding: 12rpx 16rpx;
                            border-radius: 24rpx;
                            background: #1d7bf7;
                        }
                    }
                }
                .body {
                    .message {
                        margin-top: 24rpx;
                    }
                    .images {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        grid-gap: 12rpx;
                        margin-top: 24rpx;
                        position: relative;
                        .more {
                            position: absolute;
                            right: 8rpx;
                            bottom: 8rpx;
                            width: 72rpx;
                            height: 32rpx;
                            background: rgba(0, 0, 0, 0.5);
                            border-radius: 20rpx;
                        }
                    }

                    .location {
                        margin-top: 24rpx;
                    }
                }
            }
        }
    }

    .float-btn {
        position: fixed;
        right: 32rpx;
        bottom: 200rpx;
        z-index: 999;
    }
}
</style>
