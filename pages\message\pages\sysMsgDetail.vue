<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="消息详情" isBack isShadow isPerch></c-navBar>
        </section>
        <section class="content flex flex-col gap-24">
            <text class="mf-font-28" style="color: #1a1a1a">这是标题这是标题这是标题这是标题这是标题这是标题这是标题这是标题这是标题</text>
            <text class="mf-font-24" style="color: #666666">2025-6-25</text>
            <text class="mf-font-24" style="color: #666666">
                这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容这是消息内容
            </text>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        min-height: calc(100vh - 370rpx);
        padding: 40rpx 32rpx;
    }
}
</style>
