<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="商家详情" isPerch isBack isShadow></c-navBar>
        </section>
        <section class="content">
            <view class="header">
                <view class="flex align-center justify-between">
                    <view class="left flex align-center gap-24">
                        <u-image :src="shopInfo.avatar" width="80rpx" height="80rpx" radius="8rpx" mode="aspectFill"></u-image>
                        <view class="flex align-center gap-16">
                            <text class="mf-font-28 mf-weight-bold" style="color: #191919">{{ shopInfo.title }}</text>
                            <text class="tag mf-font-20" style="color: #fff" v-if="shopInfo.tag">{{ shopInfo.tag }}</text>
                        </view>
                    </view>
                    <view class="right">
                        <view class="btn flex align-center gap-8">
                            <u-image src="/static/common/msg.png" width="24rpx" height="24rpx" mode="aspectFill"></u-image>
                            <text class="mf-font-20" style="color: #fff">私聊</text>
                        </view>
                    </view>
                </view>
                <view class="flex flex-col gap-12 mf-font-24" style="margin-top: 20rpx; color: #787b80">
                    <view class="flex align-center gap-6">
                        <u-icon name="phone" color="#787B80" size="24rpx"></u-icon>
                        <text>{{ shopInfo.phone }}</text>
                    </view>
                    <view class="flex align-center gap-6">
                        <u-icon name="map" color="#787B80" size="24rpx"></u-icon>
                        <text>{{ shopInfo.address }}</text>
                    </view>
                </view>
            </view>
            <view class="desc" style="margin-top: 24rpx">
                <text class="mf-font-28" style="color: #070f1a; margin-top: 32rpx">{{ shopInfo.desc }}</text>
            </view>
            <view class="swiper" style="margin-top: 24rpx">
                <u-swiper
                    height="300rpx"
                    indicator
                    indicatorMode="dot"
                    circular
                    :radius="4"
                    :list="shopInfo.images"
                    mode="number"
                    :effect3d="true"
                    @click="handleSwiperClick"
                ></u-swiper>
            </view>
            <view class="history">
                <view class="title mf-font-32 mf-weight-bold" style="color: #191919">历史发布</view>
                <view class="list">
                    <view class="item" v-for="(item, index) in 3" :key="index">
                        <view class="user-info flex align-center justify-between" @click="handleToUserDetail">
                            <view class="flex algin-center gap-24">
                                <u-image
                                    src="https://picsum.photos/76/76?random=1"
                                    radius="8rpx"
                                    width="76rpx"
                                    height="76rpx"
                                    mode="aspectFill"
                                ></u-image>
                                <view class="flex flex-col gap-6">
                                    <text class="mf-font-28" style="color: #191919">韩式炸鸡</text>
                                    <text class="mf-font-20" style="color: #909399">4天前发布</text>
                                </view>
                            </view>
                            <u-icon name="arrow-right" color="#666666" size="28rpx"></u-icon>
                        </view>
                        <view class="message" style="margin-top: 24rpx">
                            <text class="mf-font-28" style="color: #666">
                                麓湖生态城套三寻2位合租女室友，要求：女.不能带男朋友回家过夜的，2200元/间
                            </text>
                            <view class="flex align-center justify-between" style="margin-top: 12rpx">
                                <view class="flex align-center gap-8">
                                    <u-icon name="map" size="20rpx" color="#787B80"></u-icon>
                                    <text class="mf-font-20" style="color: #787b80">四川省成都市武汉区科创园区街105号</text>
                                </view>
                                <text class="mf-font-20" style="color: #1d7bf7">距你3km</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            shopInfo: {
                avatar: "https://picsum.photos/80/80?random=1",
                title: "韩式炸鸡",
                tag: "商家",
                time: "1小时前",
                phone: "1008611",
                address: "四川省成都市武汉区科创园区街105号",
                desc: "韩式炸鸡是一家以经营，韩式炸鸡薯条年糕等为主，融合韩国特色的炸鸡门店。",
                images: [
                    "https://picsum.photos/750/750?random=1",
                    "https://picsum.photos/750/750?random=2",
                    "https://picsum.photos/750/750?random=3",
                    "https://picsum.photos/750/750?random=4",
                ],
            },
        };
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 24rpx 32rpx;
        .header {
            .left {
                .tag {
                    background: #ff8e0d;
                    padding: 6rpx 10rpx;
                    border-radius: 4rpx;
                }
            }
            .right {
                .btn {
                    padding: 12rpx 16rpx;
                    border-radius: 24rpx;
                    background: #1d7bf7;
                }
            }
        }
        .history {
            margin-top: 32rpx;
            .list {
                margin-top: 50rpx;
                .item {
                    padding: 24rpx 0;
                    border-bottom: 2rpx solid #e6e6e6;
                }
            }
        }
    }
}
</style>
