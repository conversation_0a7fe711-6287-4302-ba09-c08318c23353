<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="游戏" customLeft isSeat isPerch color="#fff">
                <template #left>
                    <view class="leftbox flex align-center gap-10" @click="show = true">
                        <text class="mf-font-28" style="color: #fff">{{ currentArea }}</text>
                        <u-icon name="arrow-down-fill" color="#fff" size="10"></u-icon>
                    </view>
                </template>
            </c-navBar>
        </section>
        <section class="content">
            <scroll-view scroll-y class="game-list">
                <view
                    class="game-card flex flex-col gap-24"
                    v-for="(item, index) in gameList"
                    :key="index"
                    @click="$fn.jumpPage(`/pages/games/pages/gameDetail?id=${item.id}`)"
                >
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">{{ item.title }}</text>
                    <view class="flex align-center justify-between">
                        <text class="mf-font-24" style="#606266">开始时间：{{ formatStartTime(item.startTime) }}</text>
                        <view class="status flex align-center gap-8" style="color: #f50c0c" v-if="item.status === 0">
                            <view class="dot" style="background: #f50c0c"></view>
                            <text class="mf-font-24">进行中</text>
                        </view>
                        <view class="status flex align-center gap-8" style="color: #ff8800" v-if="item.status === 1">
                            <view class="dot" style="background: #ff8800"></view>
                            <text class="mf-font-24">待开始</text>
                        </view>
                    </view>
                    <view class="prize flex align-center gap-20">
                        <u-image
                            src="https://picsum.photos/96/96?random=1"
                            radius="6rpx"
                            width="96rpx"
                            height="96rpx"
                            mode="aspectFill"
                        ></u-image>
                        <view class="flex flex-col gap-20">
                            <text class="prize-title mf-font-28" style="color: #070f1a">{{ item.prize }}</text>
                            <text class="prize-desc mf-font-20" style="color: #606266">礼品数量：{{ item.prizeCount }}台</text>
                        </view>
                    </view>
                    <view class="action flex align-center justify-between" style="margin-top: 20rpx">
                        <!-- 倒计时显示 -->
                        <view class="countdown-container flex align-center gap-12" v-if="item.status === 1">
                            <text class="mf-font-24" style="color: #606266">开始倒计时</text>
                            <view class="countdown-time flex align-center gap-8">
                                <view class="time-item">
                                    <text class="time-num">{{ formatTime(item.countdown.hours) }}</text>
                                </view>
                                <text class="time-separator">:</text>
                                <view class="time-item">
                                    <text class="time-num">{{ formatTime(item.countdown.minutes) }}</text>
                                </view>
                                <text class="time-separator">:</text>
                                <view class="time-item">
                                    <text class="time-num">{{ formatTime(item.countdown.seconds) }}</text>
                                </view>
                            </view>
                        </view>
                        <!-- 进入游戏按钮 -->
                        <view
                            class="btn"
                            :style="{
                                width: item.status === 0 ? '100%' : '200rpx', // 根据状态显示不同宽度
                            }"
                        >
                            <u-button
                                type="primary"
                                size="medium"
                                :custom-style="{
                                    backgroundColor: '#1D7BF7',
                                    borderColor: '#1D7BF7',
                                    borderRadius: '16rpx',
                                    height: '80rpx',
                                }"
                            >
                                <text class="mf-font-24" style="color: #fff">进入游戏</text>
                            </u-button>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
        <section class="float-btn flex flex-col gap-12">
            <view
                class="flex flex-col flex-center gap-10"
                style="background: linear-gradient(180deg, #23cd89 0%, #00ad68 100%); border-radius: 16rpx; width: 100rpx; height: 120rpx"
                @click="$fn.jumpPage('/pages/tabbar/parse?title=规则说明')"
            >
                <u-image src="/pages/posts/static/rules.png" width="42rpx" height="48rpx" mode="aspectFill"></u-image>
                <text class="mf-font-20" style="color: #fff">规则说明</text>
            </view>
            <view
                class="flex flex-col flex-center gap-10"
                style="background: linear-gradient(180deg, #ff7734 0%, #ff5025 100%); border-radius: 16rpx; width: 100rpx; height: 120rpx"
                @click="$fn.jumpPage('/pages/games/pages/myPrize', true)"
            >
                <u-image src="/pages/posts/static/my-prize.png" width="42rpx" height="48rpx" mode="aspectFill"></u-image>
                <text class="mf-font-20" style="color: #fff">我的礼品</text>
            </view>
        </section>
        <c-tabbar :selected="currentTab" @change="handleTabChange"></c-tabbar>
        <u-picker
            :show="show"
            close-on-click-overlay
            :columns="[columns]"
            @cancel="show = false"
            @close="show = false"
            @change="changeHandler"
            @confirm="confirmHandler"
        >
        </u-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 1, // 当前选中的 tab
            columns: ["四川一区", "四川二区", "南方大区"], // 选择器数据
            currentArea: "四川一区", // 当前选择的地区
            gameList: [
                {
                    id: 1, // 游戏id
                    title: "庆祝门店十周年庆活动，满减券各种福利做游 戏免费领", // 游戏标题
                    startTime: "2025-07-29 15:30:00", // 游戏开始时间（标准格式）
                    status: 1, // 游戏状态 0: 进行中 1: 待开始 2: 已结束
                    prize: "苹果16pro max 256GB*1", // 奖品名称
                    prizeCount: 3, // 奖品数量
                    countdown: {
                        // 倒计时时间
                        hours: 0,
                        minutes: 0,
                        seconds: 0,
                    },
                    totalSeconds: 0, // 该游戏的剩余秒数
                },
                {
                    id: 2, // 游戏id
                    title: "新用户专享福利活动，注册即送大礼包", // 游戏标题
                    startTime: "2025-07-29 14:00:00", // 游戏开始时间（已开始）
                    status: 0, // 进行中
                    prize: "华为Mate60 Pro 512GB*1", // 奖品名称
                    prizeCount: 2, // 奖品数量
                    countdown: {
                        // 倒计时时间
                        hours: 0,
                        minutes: 0,
                        seconds: 0,
                    },
                    totalSeconds: 0, // 该游戏的剩余秒数
                },
            ],
            show: false, // 控制选择器的显示隐藏
            // 倒计时相关
            countdownTimers: {}, // 存储每个游戏的定时器
        };
    },

    onShow() {
        uni.hideTabBar();
        // 初始化所有游戏的倒计时
        this.initAllCountdowns();
    },
    onHide() {
        // 页面隐藏时清除所有定时器
        this.clearAllCountdowns();
    },
    onUnload() {
        // 页面卸载时清除所有定时器
        this.clearAllCountdowns();
    },
    methods: {
        // 选择地区
        changeHandler(e) {
            this.currentArea = e.value[0];
        },
        // 确认选择
        confirmHandler(e) {
            this.currentArea = e.value[0];
            this.show = false;
        },
        // 初始化所有游戏的倒计时
        initAllCountdowns() {
            this.gameList.forEach((game, index) => {
                this.initGameCountdown(game, index);
            });
        },
        // 初始化单个游戏的倒计时
        initGameCountdown(game, index) {
            // 只有状态为1（待开始）的游戏才需要倒计时
            if (game.status !== 1) {
                return;
            }

            // 解析开始时间
            const startTime = new Date(game.startTime).getTime();
            const currentTime = new Date().getTime();

            // 只有开始时间大于当前时间才开始倒计时
            if (startTime <= currentTime) {
                // 如果开始时间已过，更新状态为进行中
                this.$set(this.gameList[index], "status", 0);
                return;
            }

            // 计算剩余秒数
            const remainingSeconds = Math.floor((startTime - currentTime) / 1000);
            this.$set(this.gameList[index], "totalSeconds", remainingSeconds);

            // 更新倒计时显示
            this.updateGameCountdownDisplay(index);

            // 开始该游戏的倒计时
            this.startGameCountdown(index);
        },
        // 开始单个游戏的倒计时
        startGameCountdown(index) {
            // 清除之前的定时器
            this.clearGameCountdown(index);

            // 开始倒计时
            this.countdownTimers[index] = setInterval(() => {
                const game = this.gameList[index];
                if (game.totalSeconds > 0) {
                    this.$set(this.gameList[index], "totalSeconds", game.totalSeconds - 1);
                    this.updateGameCountdownDisplay(index);
                } else {
                    // 倒计时结束，游戏开始
                    this.clearGameCountdown(index);
                    this.onGameCountdownEnd(index);
                }
            }, 1000);
        },
        // 更新单个游戏的倒计时显示
        updateGameCountdownDisplay(index) {
            const game = this.gameList[index];
            const hours = Math.floor(game.totalSeconds / 3600);
            const minutes = Math.floor((game.totalSeconds % 3600) / 60);
            const seconds = game.totalSeconds % 60;

            this.$set(this.gameList[index], "countdown", {
                hours,
                minutes,
                seconds,
            });
        },
        // 清除单个游戏的倒计时
        clearGameCountdown(index) {
            if (this.countdownTimers[index]) {
                clearInterval(this.countdownTimers[index]);
                delete this.countdownTimers[index];
            }
        },
        // 清除所有倒计时
        clearAllCountdowns() {
            Object.keys(this.countdownTimers).forEach((index) => {
                this.clearGameCountdown(index);
            });
        },
        // 单个游戏倒计时结束处理
        onGameCountdownEnd(index) {
            // 更新游戏状态为进行中
            this.$set(this.gameList[index], "status", 0);

            uni.showToast({
                title: "游戏开始！",
                icon: "success",
            });
        },
        // 格式化时间显示（补零）
        formatTime(time) {
            return time.toString().padStart(2, "0");
        },
        // 格式化开始时间显示
        formatStartTime(timeStr) {
            const date = new Date(timeStr);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            const hours = String(date.getHours()).padStart(2, "0");
            const minutes = String(date.getMinutes()).padStart(2, "0");

            return `${year}.${month}.${day} ${hours}:${minutes}`;
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: linear-gradient(180deg, #1d7bf7 15%, #f5f6f7 30%);
    padding-bottom: 120rpx;
    .nav-bar {
        .leftbox {
            margin-left: 12rpx;
        }
    }
    .content {
        padding: 24rpx;
        .game-list {
            height: calc(100vh - 330rpx);
            .game-card {
                background: #fff;
                border-radius: 20rpx;
                padding: 28rpx 24rpx;
                margin-bottom: 20rpx;
                &:last-child {
                    margin-bottom: 0;
                }
                .status {
                    .dot {
                        width: 16rpx;
                        height: 16rpx;
                        border-radius: 50%;
                    }
                }
                .prize {
                    padding: 12rpx;
                    border-radius: 12rpx;
                    background: #f7f8fa;
                }

                // 倒计时样式
                .countdown-container {
                    .countdown-time {
                        .time-item {
                            background: #f50c0c;
                            border-radius: 8rpx;
                            padding: 8rpx;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            .time-num {
                                font-size: 24rpx;
                                font-weight: bold;
                                color: #fff;
                                line-height: 1;
                            }
                        }

                        .time-separator {
                            font-size: 24rpx;
                            font-weight: bold;
                            color: #f50c0c;
                            line-height: 1;
                        }
                    }
                }
            }
        }
    }
    .float-btn {
        position: fixed;
        right: 12rpx;
        bottom: 400rpx;
        z-index: 999;
    }
}
</style>
