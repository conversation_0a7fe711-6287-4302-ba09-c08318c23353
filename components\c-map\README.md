# C-Map 地图组件

## 组件说明
可复用的地图组件，支持自定义标记点和气泡显示。

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| latitude | Number | 30.6586 | 地图中心纬度 |
| longitude | Number | 104.0647 | 地图中心经度 |
| scale | Number | 16 | 地图缩放级别 |
| markers | Array | [] | 标记点数据数组 |
| showLocation | Boolean | true | 是否显示当前位置 |
| mapStyle | String | 'width: 100%; height: calc(100vh - 430rpx)' | 地图样式 |

## Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| bubble-tap | 气泡点击事件 | bubble: 气泡数据对象 |
| marker-tap | 标记点击事件 | event: 点击事件对象 |

## Markers 数据格式

```javascript
const markers = [
    {
        id: 0, // 唯一标识
        title: '标题', // 气泡标题
        description: '描述内容', // 气泡描述
        tag: '标签', // 可选，气泡标签
        avatar: 'https://example.com/avatar.jpg', // 头像图片
        latitude: 30.6586, // 纬度
        longitude: 104.0647, // 经度
        width: 20, // 标记宽度
        height: 2, // 标记高度
        iconPath: '/static/common/point.png', // 标记图标
        customCallout: { // 自定义气泡
            anchorX: 0, // 气泡X轴偏移
            anchorY: -5, // 气泡Y轴偏移
            display: 'ALWAYS'  // 气泡显示方式 ALWAYS 显示，NEVER 不显示
        }
    }
]
```

## 使用示例

```vue
<template>
    <view>
        <c-map
            :latitude="30.6586"
            :longitude="104.0647"
            :scale="16"
            :markers="mapMarkers"
            :show-location="true"
            map-style="width: 100%; height: 500rpx"
            @bubble-tap="handleBubbleTap"
            @marker-tap="handleMarkerTap"
        ></c-map>
    </view>
</template>

<script>
export default {
    data() {
        return {
            mapMarkers: [
                {
                    id: 0,
                    title: '韩式炸鸡',
                    description: '满减券大放送...',
                    tag: '商家',
                    avatar: 'https://picsum.photos/64/64?random=1',
                    latitude: 30.6586,
                    longitude: 104.0647,
                    width: 20,
                    height: 2,
                    iconPath: '/static/common/point.png',
                    customCallout: {
                        anchorX: 0,
                        anchorY: -5,
                        display: 'ALWAYS'
                    }
                }
            ]
        }
    },
    methods: {
        handleBubbleTap(bubble) {
            console.log('气泡点击:', bubble);
            uni.showModal({
                title: bubble.title,
                content: bubble.description,
                showCancel: false
            });
        },
        handleMarkerTap(event) {
            console.log('标记点击:', event);
        }
    }
}
</script>
```

## 注意事项

1. 组件使用了 `cover-view` 和 `cover-image`，确保在地图上层显示
2. 气泡样式已经过优化，在各平台兼容性良好
3. 箭头使用 Unicode 字符，兼容性最佳
4. 需要在使用页面中处理定位权限和数据生成逻辑
