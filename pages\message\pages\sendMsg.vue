<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="王淑芳" isBack isShadow isPerch>
                <template #right>
                    <view class="nav-right flex align-center gap-24">
                        <u-icon name="more-dot-fill" size="32rpx" color="#191919"></u-icon>
                        <u-icon name="phone-fill" size="32rpx" color="#191919"></u-icon>
                    </view>
                </template>
            </c-navBar>
        </section>
        <section class="content">
            <scroll-view class="chat-container" scroll-y :scroll-top="scrollTop" scroll-with-animation>
                <!-- 时间分割线 -->
                <view class="time-divider">
                    <text class="time-text">14:32</text>
                </view>

                <!-- 聊天消息列表 -->
                <view class="message-list">
                    <view v-for="(message, index) in messageList" :key="index" class="message-item" :class="message.type">
                        <!-- 接收的消息 -->
                        <view v-if="message.type === 'received'" class="message-wrapper flex align-start gap-16">
                            <u-image :src="message.avatar" width="72rpx" height="72rpx" radius="36rpx" mode="aspectFill"></u-image>
                            <view class="message-content received-content">
                                <text class="message-text">{{ message.content }}</text>
                            </view>
                        </view>

                        <!-- 发送的消息 -->
                        <view v-else class="message-wrapper flex align-start gap-16 justify-end">
                            <view class="message-content sent-content">
                                <text class="message-text">{{ message.content }}</text>
                            </view>
                            <u-image :src="message.avatar" width="72rpx" height="72rpx" radius="36rpx" mode="aspectFill"></u-image>
                        </view>

                        <!-- 消息时间 -->
                        <view v-if="message.showTime" class="message-time">
                            <text class="time-text">{{ message.time }}</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>

        <!-- 底部输入区域 -->
        <section class="footer">
            <view class="input-container flex align-center gap-12">
                <u-input
                    v-model="inputMessage"
                    placeholder="请输入消息..."
                    :customStyle="{
                        backgroundColor: '#f7f7f7',
                        borderRadius: '12rpx',
                        height: '72rpx',
                        padding: '0 24rpx',
                        flex: '1',
                    }"
                    border="none"
                    @confirm="sendMessage"
                    confirm-type="send"
                />
                <u-button
                    type="primary"
                    size="small"
                    :custom-style="{
                        backgroundColor: '#1D7BF7',
                        borderColor: '#1D7BF7',
                        borderRadius: '12rpx',
                        width: '120rpx',
                        height: '72rpx',
                    }"
                    @click="sendMessage"
                >
                    <text class="mf-font-28" style="color: #fff">发送</text>
                </u-button>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            inputMessage: "", // 输入的消息
            scrollTop: 0, // 滚动位置
            messageList: [
                {
                    type: "received",
                    content: "亲爱的，王淑芳，我来看望您",
                    avatar: "https://picsum.photos/72/72?random=1",
                    time: "14:32",
                    showTime: false,
                },
                {
                    type: "received",
                    content: "你在做什么！",
                    avatar: "https://picsum.photos/72/72?random=1",
                    time: "14:32",
                    showTime: true,
                },
                {
                    type: "sent",
                    content: "亲爱的，想念你，我来关注分享",
                    avatar: "https://picsum.photos/72/72?random=2",
                    time: "14:32",
                    showTime: false,
                },
                {
                    type: "sent",
                    content: "我想在什么地方有空，正在和一个朋友过生日的聊天",
                    avatar: "https://picsum.photos/72/72?random=2",
                    time: "14:32",
                    showTime: false,
                },
            ],
        };
    },

    mounted() {
        this.scrollToBottom();
    },

    methods: {
        // 发送消息
        sendMessage() {
            if (!this.inputMessage.trim()) {
                return;
            }

            const newMessage = {
                type: "sent",
                content: this.inputMessage,
                avatar: "https://picsum.photos/72/72?random=2",
                time: this.getCurrentTime(),
                showTime: true,
            };

            this.messageList.push(newMessage);
            this.inputMessage = "";

            // 滚动到底部
            this.$nextTick(() => {
                this.scrollToBottom();
            });
        },

        // 获取当前时间
        getCurrentTime() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, "0");
            const minutes = String(now.getMinutes()).padStart(2, "0");
            return `${hours}:${minutes}`;
        },

        // 滚动到底部
        scrollToBottom() {
            this.scrollTop = 999999;
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    height: 100vh;
    display: flex;
    flex-direction: column;

    .content {
        flex: 1;
        overflow: hidden;
        padding: 32rpx;

        .chat-container {
            height: calc(100vh - 200rpx - 200rpx); // 减去导航栏和底部输入框高度

            .time-divider {
                text-align: center;
                margin: 32rpx 0;

                .time-text {
                    font-size: 24rpx;
                    color: #999;
                    // background: rgba(255, 255, 255, 0.8);
                    // padding: 8rpx 16rpx;
                    // border-radius: 16rpx;
                }
            }

            .message-list {
                .message-item {
                    margin-bottom: 32rpx;

                    .message-wrapper {
                        max-width: 100%;

                        .message-content {
                            max-width: 480rpx;
                            padding: 20rpx 24rpx;
                            border-radius: 20rpx;
                            position: relative;

                            .message-text {
                                font-size: 32rpx;
                                line-height: 1.4;
                                word-wrap: break-word;
                            }

                            // 接收消息样式
                            &.received-content {
                                background: #fff;
                                color: #333;
                                border-top-left-radius: 8rpx;

                                &::before {
                                    content: "";
                                    position: absolute;
                                    left: -16rpx;
                                    top: 20rpx;
                                    width: 0;
                                    height: 0;
                                    border: 8rpx solid transparent;
                                    border-right-color: #fff;
                                }
                            }

                            // 发送消息样式
                            &.sent-content {
                                background: #1d7bf7;
                                color: #fff;
                                border-top-right-radius: 8rpx;

                                &::after {
                                    content: "";
                                    position: absolute;
                                    right: -16rpx;
                                    top: 20rpx;
                                    width: 0;
                                    height: 0;
                                    border: 8rpx solid transparent;
                                    border-left-color: #1d7bf7;
                                }
                            }
                        }
                    }

                    .message-time {
                        text-align: center;
                        margin-top: 16rpx;

                        .time-text {
                            font-size: 24rpx;
                            color: #999;
                        }
                    }
                }
            }
        }
    }

    .footer {
        padding: 24rpx 32rpx;
        padding-bottom: calc(24rpx + env(safe-area-inset-bottom));

        .input-container {
            width: 100%;
        }
    }
}
</style>
