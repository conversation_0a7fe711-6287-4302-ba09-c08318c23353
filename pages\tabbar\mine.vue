<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="个人中心" isSeat isPerch></c-navBar>
        </section>
        <section class="content">
            <view class="user-info flex align-center gap-24">
                <view class="avatar">
                    <u-image
                        src="https://picsum.photos/100/100?random=1"
                        width="144rpx"
                        height="144rpx"
                        shape="circle"
                        mode="aspectFill"></u-image>
                </view>
                <view class="info flex-1 flex align-center justify-between">
                    <view class="left flex flex-col gap-16">
                        <view class="flex align-center gap-16">
                            <text class="mf-font-40 mf-weight-bold" style="color: #1a1a1a">张三三</text>
                            <u-image src="/static/common/is-auth.png" v-if="false" width="82rpx" height="32rpx" mode="aspectFill"></u-image>
                            <u-image src="/static/common/is-auth-1.png" width="82rpx" height="32rpx" mode="aspectFill"></u-image>
                        </view>
                        <text class="mf-font-28" style="color: #666666">158****7890</text>
                    </view>
                    <u-icon name="arrow-right" size="24rpx" color="#666666"></u-icon>
                </view>
            </view>
            <view class="user-total flex align-center gap-24">
                <view class="item flex flex-col flex-center gap-10">
                    <text class="mf-font-40 mf-weight-bold">999.99</text>
                    <text class="mf-font-24">余额(元)</text>
                </view>
                <view class="line"></view>
                <view class="item flex flex-col flex-center gap-10">
                    <text class="mf-font-40 mf-weight-bold">15</text>
                    <text class="mf-font-24">优惠券</text>
                </view>
                <view class="line"></view>
                <view class="item flex flex-col flex-center gap-10">
                    <text class="mf-font-40 mf-weight-bold">12</text>
                    <text class="mf-font-24">我的发布</text>
                </view>
            </view>
            <view class="card">
                <text class=""></text>
            </view>
        </section>
        <c-tabbar :selected="currentTab" @change="handleTabChange"></c-tabbar>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 4,
        };
    },
    onShow() {
        uni.hideTabBar();
    },
    methods: {
        handleTabChange(index) {
            // this.currentTab = index;
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: linear-gradient(180deg, #d9ebff 30%, #f7f7f7 40%);
    padding-bottom: 120rpx;
    .content {
        min-height: calc(100vh - 370rpx);
        padding: 40rpx;

        .user-info {
            padding: 40rpx 0;
        }
        .user-total {
            .line {
                width: 1rpx;
                height: 30rpx;
                background-color: #999;
            }

            .item {
                flex: 1;
            }
        }
        .card {
            margin-top: 32rpx;
            padding: 24rpx;
            background: #fff;
            border-radius: 12rpx;
        }
    }
}
</style>
