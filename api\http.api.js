import dApi from "./divide/d-api.js";
import qApi from "./divide/q-api.js";

const http = uni.$u.http;
const getApi = (params = {}, config) => http.get("/frontend/user/login", { params, ...config });
//  ↑ get
//  ↓ post
const postApi = (data = {}, config) => http.post("/frontend/login/account-password", data, config);

let apiList = {
    ...dApi,
    ...qApi,
    getApi, // get接口示例
    postApi, // post接口示例
};
export default { ...apiList };
