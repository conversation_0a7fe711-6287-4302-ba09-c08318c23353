<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="游戏详情" isShadow isBack isPerch> </c-navBar>
        </section>
        <section class="content">
            <view class="swiper">
                <u-swiper :list="list" indicator indicatorMode="dot" height="320rpx" circular></u-swiper>
            </view>
            <view class="header">
                <view class="flex align-center justify-between">
                    <text class="mf-font-28 mf-weight-bold" style="color: #191919">活动名称活动名称活动名称</text>
                    <u-icon name="share-square" bold color="#191919" size="28rpx"></u-icon>
                </view>
                <view class="flex align-center justify-between" style="margin-top: 20rpx">
                    <text class="mf-font-28" style="color: #333333">开始时间：{{ gameDetail.startTime }}</text>
                    <view class="flex align-center gap-8" @click="$fn.jumpPage('/pages/tabbar/parse?title=游戏规则')">
                        <u-icon
                            name="error-circle"
                            color="#1D7BF7"
                            label="游戏规则"
                            label-color="#1d7bf7"
                            label-size="24rpx"
                            :top="1"
                            size="24rpx"
                        ></u-icon>
                    </view>
                </view>
            </view>
            <view class="prize">
                <text class="mf-font-28 mf-weight-bold" style="color: #191919">活动礼品</text>
                <view class="prize-item flex align-center gap-20">
                    <u-image
                        src="https://picsum.photos/96/96?random=1"
                        radius="6rpx"
                        width="96rpx"
                        height="96rpx"
                        mode="aspectFill"
                    ></u-image>
                    <view class="flex flex-col gap-20">
                        <text class="prize-title mf-font-28" style="color: #070f1a">苹果16pro max 256GB*1</text>
                        <text class="prize-desc mf-font-20" style="color: #606266">礼品数量：3台</text>
                    </view>
                </view>
            </view>
            <!-- 游戏信息 -->
            <view class="game-info flex align-center">
                <view class="item flex flex-col flex-center">
                    <text class="label">报名人数</text>
                    <text class="value">{{ gameDetail.totalPlayers }}</text>
                </view>
                <view class="item flex flex-col flex-center active">
                    <text class="label">当前轮次</text>
                    <text class="value">第{{ gameDetail.currentRound }}轮</text>
                </view>
                <view class="item flex flex-col flex-center">
                    <text class="label">已淘汰</text>
                    <text class="value">{{ gameDetail.eliminatedCount }}</text>
                </view>
                <view class="item flex flex-col flex-center">
                    <text class="label">剩余</text>
                    <text class="value">{{ gameDetail.remainingCount }}</text>
                </view>
            </view>
            <!-- 游戏状态 -->
            <view class="game-status">
                <!-- 待开始状态 -->
                <view v-if="gameDetail.status === 1" class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">等待游戏开始</text>
                    <text class="mf-font-24" style="color: #191919">{{ `报名人数：${gameDetail.totalPlayers} 人` }}</text>
                </view>

                <!-- 进行中 - 选择状态 -->
                <view v-else-if="gameDetail.status === 2 && gameDetail.userGameStatus === 'choosing'" class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">第{{ gameDetail.currentRound }}轮倒计时</text>
                    <text class="mf-font-24" style="color: #191919">{{ totalSeconds }}秒</text>
                </view>

                <!-- 晋级状态 -->
                <view v-else-if="gameDetail.userGameStatus === 'advanced'" class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">您已晋级第{{ gameDetail.currentRound + 1 }}轮</text>
                    <text class="mf-font-24" style="color: #191919">下一轮倒计时{{ totalSeconds }}秒</text>
                </view>

                <!-- 淘汰状态 -->
                <view v-else-if="gameDetail.userGameStatus === 'eliminated'" class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">您已被淘汰</text>
                    <text class="mf-font-24" style="color: #191919">别灰心！下次再参与</text>
                </view>

                <!-- 获奖状态 -->
                <view v-else-if="gameDetail.userGameStatus === 'winner'" class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">您已获奖</text>
                    <text class="mf-font-24" style="color: #191919">恭喜您！快去领奖吧！</text>
                </view>

                <!-- 游戏结束状态 -->
                <view v-else-if="gameDetail.status === 3" class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">游戏已结束</text>
                    <text class="mf-font-24" style="color: #191919">感谢您的参与！</text>
                </view>

                <!-- 等待状态 -->
                <view v-else class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">等待游戏开始</text>
                    <text class="mf-font-24" style="color: #191919">游戏即将开始</text>
                </view>
            </view>
            <!-- 游戏选择 -->
            <view v-if="showChoiceArea" class="game-choice flex align-center gap-12">
                <!-- 选择阶段：1:1显示，不显示人数 -->
                <view v-if="!gameDetail.currentRoundData.showResult" class="left flex flex-center" :style="{ width: '50%' }" @click="handleChoice('red')">
                    <text class="mf-font-28" style="color: #fff">红方</text>
                </view>
                <view v-if="!gameDetail.currentRoundData.showResult" class="right flex flex-center" :style="{ width: '50%' }" @click="handleChoice('blue')">
                    <text class="mf-font-28" style="color: #fff">蓝方</text>
                </view>

                <!-- 结果阶段：按比例显示，显示人数 -->
                <view v-if="gameDetail.currentRoundData.showResult" class="left flex flex-center"
                      :style="{ width: (gameDetail.currentRoundData.redCount / (gameDetail.currentRoundData.redCount + gameDetail.currentRoundData.blueCount) * 100) + '%' }">
                    <text class="mf-font-28" style="color: #fff">红方({{ gameDetail.currentRoundData.redCount }})</text>
                </view>
                <view v-if="gameDetail.currentRoundData.showResult" class="right flex flex-center"
                      :style="{ width: (gameDetail.currentRoundData.blueCount / (gameDetail.currentRoundData.redCount + gameDetail.currentRoundData.blueCount) * 100) + '%' }">
                    <text class="mf-font-28" style="color: #fff">蓝方({{ gameDetail.currentRoundData.blueCount }})</text>
                </view>
            </view>
            <!-- 倒计时显示 - 只在待开始状态显示 -->
            <view class="countdown-container flex align-center justify-center gap-12" v-if="gameDetail.status === 1">
                <text class="mf-font-24" style="color: #070f1a">开始倒计时</text>
                <view class="countdown-time flex align-center gap-8">
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.hours) }}</text>
                    </view>
                    <text class="time-separator">:</text>
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.minutes) }}</text>
                    </view>
                    <text class="time-separator">:</text>
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.seconds) }}</text>
                    </view>
                </view>
            </view>
            <!-- 操作按钮 -->
            <view class="btn flex flex-center">
                <!-- 报名按钮 - 仅在待开始状态且未报名时显示 -->
                <u-button
                    v-if="gameDetail.status === 1 && !gameDetail.isRegistered"
                    type="primary"
                    size="large"
                    :custom-style="{
                        backgroundColor: '#1D7BF7',
                        borderColor: '#1D7BF7',
                        borderRadius: '32rpx',
                        width: '414rpx',
                        height: '64rpx',
                    }"
                    @click="handleRegister"
                >
                    <view class="flex align-center gap-12">
                        <text class="mf-font-24" style="color: #fff">立即报名</text>
                    </view>
                </u-button>

                <!-- 进入下一轮按钮 - 仅在晋级状态显示 -->
                <u-button
                    v-if="gameDetail.userGameStatus === 'advanced'"
                    type="primary"
                    size="large"
                    :custom-style="{
                        backgroundColor: '#1D7BF7',
                        borderColor: '#1D7BF7',
                        borderRadius: '32rpx',
                        width: '414rpx',
                        height: '64rpx',
                    }"
                    @click="enterNextRound"
                >
                    <view class="flex align-center gap-12">
                        <text class="mf-font-24" style="color: #fff">进入下一轮</text>
                    </view>
                </u-button>

                <!-- 获奖用户按钮 - 只在游戏结束后显示 -->
                <u-button
                    v-if="gameDetail.status === 3"
                    type="primary"
                    plain
                    size="large"
                    :custom-style="{
                        borderRadius: '32rpx',
                        width: '256rpx',
                        height: '64rpx',
                    }"
                    @click="$fn.jumpPage('/pages/games/pages/winningUsers', true)"
                >
                    <view class="flex align-center gap-12">
                        <text class="mf-font-24" style="color: #1D7BF7">获奖用户</text>
                    </view>
                </u-button>

                <!-- 领取奖品按钮 - 仅在获奖状态显示 -->
                <u-button
                    v-if="gameDetail.userGameStatus === 'winner'"
                    type="primary"
                    size="large"
                    :custom-style="{
                        backgroundColor: '#1D7BF7',
                        borderColor: '#1D7BF7',
                        borderRadius: '32rpx',
                        width: '256rpx',
                        height: '64rpx',
                    }"
                    @click="$fn.jumpPage('/pages/games/pages/claimYourPrize', true)"
                >
                    <view class="flex align-center gap-12">
                        <text class="mf-font-24" style="color: #fff">领取奖品</text>
                    </view>
                </u-button>
            </view>
            <!-- 弹幕 - 游戏结束后隐藏 -->
            <view v-if="gameDetail.status !== 3" class="bullet-comment">
                <c-bulletComment ref="bulletComment" class="bullet-content" @reloadDanmu="reloadDanmu"></c-bulletComment>
            </view>

            <!-- 获奖名单 - 游戏结束后显示 -->
            <view v-if="gameDetail.status === 3" class="winner-list">
                <view class="winner-header">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">游戏已结束</text>
                    <view class="winner-stats flex align-center justify-center gap-40" style="margin-top: 20rpx;">
                        <text class="mf-font-24" style="color: #666;">报名人数：{{ gameDetail.totalPlayers }}人</text>
                        <text class="mf-font-24" style="color: #666;">获奖人数：{{ winnerList.length }}人</text>
                    </view>
                </view>

                <view class="winner-title" style="margin-top: 40rpx;">
                    <text class="mf-font-28 mf-weight-bold" style="color: #191919">获奖名单</text>
                </view>

                <view class="winner-items" style="margin-top: 24rpx;">
                    <view v-for="(winner, index) in winnerList" :key="index" class="winner-item flex align-center gap-20" style="margin-bottom: 24rpx; padding: 20rpx; background: #f8f9fa; border-radius: 12rpx;">
                        <u-image
                            :src="winner.prizeImage"
                            radius="6rpx"
                            width="96rpx"
                            height="96rpx"
                            mode="aspectFill"
                        ></u-image>
                        <view class="winner-info flex flex-col gap-12">
                            <text class="winner-prize mf-font-28" style="color: #070f1a">{{ winner.prizeName }}</text>
                            <text class="winner-user mf-font-20" style="color: #606266">获奖用户：{{ winner.userName }}</text>
                            <text class="winner-time mf-font-20" style="color: #606266">获奖时间：{{ winner.winTime }}</text>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 调试面板 - 开发时使用 -->
            <view class="debug-panel" style="margin-top: 40rpx; padding: 20rpx; background: #f0f0f0; border-radius: 12rpx;">
                <text class="mf-font-24 mf-weight-bold" style="color: #333;">调试面板</text>
                <view class="debug-info" style="margin-top: 16rpx;">
                    <text class="mf-font-20" style="color: #666;">游戏状态: {{ gameDetail.status === 1 ? '待开始' : gameDetail.status === 2 ? '进行中' : '已结束' }}</text>
                    <text class="mf-font-20" style="color: #666; margin-left: 20rpx;">用户状态: {{ gameDetail.userGameStatus }}</text>
                </view>
                <view class="debug-info" style="margin-top: 8rpx;">
                    <text class="mf-font-20" style="color: #666;">倒计时类型: {{ countdownType }}</text>
                    <text class="mf-font-20" style="color: #666; margin-left: 20rpx;">剩余时间: {{ totalSeconds }}秒</text>
                </view>
                <view class="debug-info" style="margin-top: 8rpx;">
                    <text class="mf-font-20" style="color: #666;">用户选择: {{ gameDetail.userChoice || '未选择' }}</text>
                    <text class="mf-font-20" style="color: #666; margin-left: 20rpx;">是否报名: {{ gameDetail.isRegistered ? '是' : '否' }}</text>
                </view>
                <view class="debug-buttons" style="margin-top: 16rpx; display: flex; gap: 12rpx; flex-wrap: wrap;">
                    <button @click="debugStartGame" style="padding: 8rpx 16rpx; font-size: 20rpx;">开始游戏</button>
                    <button @click="debugRegister" style="padding: 8rpx 16rpx; font-size: 20rpx;">模拟报名</button>
                    <button @click="debugShowResult" style="padding: 8rpx 16rpx; font-size: 20rpx;">显示结果</button>
                    <button @click="debugEndGame" style="padding: 8rpx 16rpx; font-size: 20rpx;">结束游戏</button>
                    <button @click="debugReset" style="padding: 8rpx 16rpx; font-size: 20rpx;">重置游戏</button>
                </view>
            </view>

            <!-- 发送弹幕输入框 - 游戏结束后隐藏 -->
            <view v-if="gameDetail.status !== 3" class="send-message">
                <u-input
                    v-model="bulletComment"
                    placeholder="说点什么..."
                    :customStyle="{
                        borderRadius: '36rpx',
                        height: '72rpx',
                        background: '#F5F6F7',
                        padding: '0 24rpx',
                    }"
                    border="none"
                    clearable
                    confirmType="send"
                    @confirm="sendBulletComment"
                />
            </view>
        </section>
    </view>
</template>

<script>
export default {
    computed: {
        // 计算当前应该显示的按钮类型
        currentButtonType() {
            if (this.gameDetail.status === 1 && !this.gameDetail.isRegistered) {
                return 'register'; // 显示报名按钮
            } else if (this.gameDetail.userGameStatus === 'advanced') {
                return 'nextRound'; // 显示进入下一轮按钮
            } else if (this.gameDetail.userGameStatus === 'winner') {
                return 'claimPrize'; // 显示领取奖品按钮
            }
            return 'none'; // 不显示操作按钮
        },

        // 计算选择区域的显示状态
        showChoiceArea() {
            return this.gameDetail.status === 2 &&
                   (this.gameDetail.userGameStatus === 'choosing' || this.gameDetail.currentRoundData.showResult);
        }
    },
    data() {
        return {
            list: [
                "https://picsum.photos/750/750?random=1",
                "https://picsum.photos/750/750?random=2",
                "https://picsum.photos/750/750?random=3",
                "https://picsum.photos/750/750?random=4",
            ],
            // 游戏详情数据
            gameDetail: {
                id: 1,
                title: "庆祝门店十周年庆活动，满减券各种福利做游戏免费领",
                startTime: "2025-07-30 20:30:00", // 游戏开始时间
                status: 1, // 游戏状态: 1-待开始, 2-进行中, 3-已结束
                prize: "苹果16pro max 256GB*1",
                prizeCount: 3,
                totalPlayers: 200, // 总报名人数
                currentRound: 1, // 当前轮次
                eliminatedCount: 0, // 已淘汰人数
                remainingCount: 200, // 剩余人数
                winnerCount: 3, // 获奖人数（变量控制）
                countdown: {
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                },
                // 用户游戏状态
                isRegistered: false, // 是否已报名
                userGameStatus: 'waiting', // 用户游戏状态: waiting-等待开始, choosing-选择中, advanced-晋级, eliminated-淘汰, winner-获奖
                userChoice: null, // 用户选择: 'red'-红方, 'blue'-蓝方, null-未选择
                // 当前轮次数据
                currentRoundData: {
                    redCount: 0, // 红方人数
                    blueCount: 0, // 蓝方人数
                    showResult: false, // 是否显示结果
                    winnerSide: null, // 获胜方: 'red' 或 'blue'
                }
            },
            // 游戏配置
            gameConfig: {
                choiceTimeLimit: 10, // 选择时间限制（秒）
                nextRoundWaitTime: 10, // 进入下一轮等待时间（秒）
            },
            // 倒计时相关
            totalSeconds: 0, // 当前倒计时总秒数
            countdownType: 'start', // 倒计时类型: 'start'-开始倒计时, 'choice'-选择倒计时, 'nextRound'-下一轮倒计时
            countdownTimer: null, // 倒计时定时器
            bulletComment: "", // 弹幕相关
            danmuList: [], // 弹幕数据列表
            danmuContion: {
                // 弹幕查询条件
                page: 1,
                size: 200,
            },
            userInfo: {
                nickname: "游客",
                avatar: "https://picsum.photos/60/60?random=1",
            },
            // 弹幕重载控制
            lastReloadTime: 0, // 上次重载时间
            reloadCooldown: 5000, // 重载冷却时间 5秒

            // 获奖名单
            winnerList: [
                {
                    prizeName: "苹果16pro max 256GB*1",
                    userName: "用户李四",
                    winTime: "2025-7-9 12:00:20",
                    prizeImage: "https://picsum.photos/96/96?random=1"
                },
                {
                    prizeName: "苹果16pro max 256GB*1",
                    userName: "用户李四",
                    winTime: "2025-7-9 12:00:20",
                    prizeImage: "https://picsum.photos/96/96?random=2"
                }
            ],
        };
    },
    onLoad(options) {
        // 获取游戏ID
        if (options.id) {
            this.gameDetail.id = options.id;
        }
        // 初始化倒计时
        this.initCountdown();
        // 初始化弹幕
        this.initBulletComment();
    },
    onShow() {
        // 页面显示时重新初始化倒计时
        this.initCountdown();
    },
    onHide() {
        // 页面隐藏时清除定时器
        this.clearCountdown();
    },
    onUnload() {
        // 页面卸载时清除定时器
        this.clearCountdown();
    },
    methods: {
        // 初始化倒计时
        initCountdown() {
            // 根据游戏状态初始化不同的倒计时
            if (this.gameDetail.status === 1) {
                // 待开始状态 - 开始倒计时
                this.initStartCountdown();
            } else if (this.gameDetail.status === 2) {
                // 进行中状态 - 根据用户状态决定倒计时类型
                this.initGameCountdown();
            }
        },

        // 初始化开始倒计时
        initStartCountdown() {
            const startTime = new Date(this.gameDetail.startTime).getTime();
            const currentTime = new Date().getTime();

            if (startTime <= currentTime) {
                // 开始时间已过，进入游戏状态
                this.startGame();
                return;
            }

            // 设置开始倒计时
            this.countdownType = 'start';
            this.totalSeconds = Math.floor((startTime - currentTime) / 1000);
            this.updateCountdownDisplay();
            this.startCountdown();
        },

        // 初始化游戏中的倒计时
        initGameCountdown() {
            if (this.gameDetail.userGameStatus === 'choosing') {
                // 选择倒计时
                this.startChoiceCountdown();
            } else if (this.gameDetail.userGameStatus === 'advanced') {
                // 下一轮等待倒计时
                this.startNextRoundCountdown();
            }
        },
        // 开始倒计时
        startCountdown() {
            this.clearCountdown();

            this.countdownTimer = setInterval(() => {
                if (this.totalSeconds > 0) {
                    this.totalSeconds--;
                    this.updateCountdownDisplay();
                } else {
                    this.clearCountdown();
                    this.onCountdownEnd();
                }
            }, 1000);
        },

        // 开始选择倒计时
        startChoiceCountdown() {
            this.countdownType = 'choice';
            this.totalSeconds = this.gameConfig.choiceTimeLimit;
            this.updateCountdownDisplay();
            this.startCountdown();
        },

        // 开始下一轮等待倒计时
        startNextRoundCountdown() {
            this.countdownType = 'nextRound';
            this.totalSeconds = this.gameConfig.nextRoundWaitTime;
            this.updateCountdownDisplay();
            this.startCountdown();
        },
        // 更新倒计时显示
        updateCountdownDisplay() {
            const hours = Math.floor(this.totalSeconds / 3600);
            const minutes = Math.floor((this.totalSeconds % 3600) / 60);
            const seconds = this.totalSeconds % 60;

            this.gameDetail.countdown = {
                hours,
                minutes,
                seconds,
            };
        },
        // 清除倒计时
        clearCountdown() {
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
                this.countdownTimer = null;
            }
        },
        // 倒计时结束处理
        onCountdownEnd() {
            switch (this.countdownType) {
                case 'start':
                    this.onStartCountdownEnd();
                    break;
                case 'choice':
                    this.onChoiceCountdownEnd();
                    break;
                case 'nextRound':
                    this.onNextRoundCountdownEnd();
                    break;
            }
        },

        // 开始倒计时结束
        onStartCountdownEnd() {
            if (this.gameDetail.isRegistered) {
                // 已报名用户进入选择状态
                this.startGame();
            } else {
                // 未报名用户无法参与
                uni.showToast({
                    title: "游戏已开始，无法报名",
                    icon: "none"
                });
            }
        },

        // 选择倒计时结束
        onChoiceCountdownEnd() {
            if (!this.gameDetail.userChoice) {
                // 未选择，淘汰
                this.gameDetail.userGameStatus = 'eliminated';
                uni.showToast({
                    title: "未选择，已被淘汰",
                    icon: "none"
                });
            } else {
                // 已选择，等待结果
                this.showRoundResult();
            }
        },

        // 下一轮倒计时结束
        onNextRoundCountdownEnd() {
            // 自动淘汰未点击进入下一轮的用户
            this.gameDetail.userGameStatus = 'eliminated';
            uni.showToast({
                title: "未进入下一轮，已被淘汰",
                icon: "none"
            });
        },

        // 开始游戏
        startGame() {
            this.gameDetail.status = 2; // 进行中
            if (this.gameDetail.isRegistered) {
                this.gameDetail.userGameStatus = 'choosing';
                this.startChoiceCountdown();
                uni.showToast({
                    title: "游戏开始！请选择红方或蓝方",
                    icon: "success"
                });
            }
        },
        // 格式化时间显示（补零）
        formatTime(time) {
            return time.toString().padStart(2, "0");
        },

        // ========== 游戏核心逻辑方法 ==========

        // 用户报名
        handleRegister() {
            if (this.gameDetail.status !== 1) {
                uni.showToast({
                    title: "游戏已开始，无法报名",
                    icon: "none"
                });
                return;
            }

            this.gameDetail.isRegistered = true;
            this.gameDetail.totalPlayers++;
            this.gameDetail.remainingCount++;

            uni.showToast({
                title: "报名成功！",
                icon: "success"
            });
        },

        // 用户选择红方或蓝方
        handleChoice(choice) {
            if (this.gameDetail.userGameStatus !== 'choosing') {
                uni.showToast({
                    title: "当前无法选择",
                    icon: "none"
                });
                return;
            }

            if (this.gameDetail.userChoice) {
                uni.showToast({
                    title: "已经选择过了",
                    icon: "none"
                });
                return;
            }

            this.gameDetail.userChoice = choice;
            uni.showToast({
                title: `选择${choice === 'red' ? '红方' : '蓝方'}成功`,
                icon: "success"
            });
        },

        // 显示轮次结果
        showRoundResult() {
            // 模拟获取轮次结果数据
            this.gameDetail.currentRoundData = {
                redCount: Math.floor(Math.random() * 50) + 20,
                blueCount: Math.floor(Math.random() * 50) + 20,
                showResult: true
            };

            // 确定获胜方
            const { redCount, blueCount } = this.gameDetail.currentRoundData;
            this.gameDetail.currentRoundData.winnerSide = redCount > blueCount ? 'red' : 'blue';

            // 判断用户是否晋级
            const userWon = this.gameDetail.userChoice === this.gameDetail.currentRoundData.winnerSide;

            if (userWon) {
                // 检查是否达到获奖条件
                const winnerCount = Math.min(redCount, blueCount);
                if (winnerCount <= this.gameDetail.winnerCount) {
                    this.gameDetail.userGameStatus = 'winner';
                    this.gameDetail.status = 3; // 设置游戏结束状态
                    this.clearCountdown(); // 清除倒计时
                    uni.showToast({
                        title: "恭喜获奖！",
                        icon: "success"
                    });
                } else {
                    // 晋级，开始下一轮等待倒计时
                    this.startAdvancedWaiting();
                    uni.showToast({
                        title: "恭喜晋级！",
                        icon: "success"
                    });
                }
            } else {
                this.gameDetail.userGameStatus = 'eliminated';
                uni.showToast({
                    title: "很遗憾，您被淘汰了",
                    icon: "none"
                });
            }
        },

        // 进入下一轮
        enterNextRound() {
            if (this.gameDetail.userGameStatus !== 'advanced') {
                return;
            }

            // 重置选择状态
            this.gameDetail.userChoice = null;
            this.gameDetail.currentRoundData.showResult = false;
            this.gameDetail.currentRound++;
            this.gameDetail.userGameStatus = 'choosing';

            // 继续当前倒计时，不重新开始
            // 如果倒计时已经结束，则开始新的选择倒计时
            if (this.totalSeconds <= 0) {
                this.startChoiceCountdown();
            } else {
                // 切换倒计时类型为选择倒计时，但保持当前剩余时间
                this.countdownType = 'choice';
            }

            uni.showToast({
                title: `进入第${this.gameDetail.currentRound}轮`,
                icon: "success"
            });
        },

        // 开始下一轮等待（晋级后的等待状态）
        startAdvancedWaiting() {
            this.gameDetail.userGameStatus = 'advanced';
            this.startNextRoundCountdown();
        },

        // ========== 弹幕相关方法 ==========

        // 获取弹幕列表
        async getBarrageList(isInit) {
            try {
                // TODO: 替换为实际的API调用
                // let res = await getBarrageListApi(this.danmuContion)
                // let resData = (res && res.data) || {}
                // let list = Array.isArray(resData.records) ? resData.records : []

                // 模拟API数据
                let list = [];
                for (let i = 0; i < 20; i++) {
                    list.push({
                        nickname: `用户${i + 1}`,
                        voteName: this.gameDetail.title || "游戏",
                        avatarUrl: `https://picsum.photos/44/44?random=${i}`,
                        content: `用户${i + 1} 已为《${this.gameDetail.title || "游戏"}》投下宝贵的一票`,
                    });
                }

                // 处理弹幕数据
                list.map((item) => {
                    item.color = "#000000"; // 黑色文字
                    item.timestampt = new Date().getTime();
                    // 不添加图片，只保留文字内容
                    item.content = `${item.nickname} 已为《${item.voteName}》投下宝贵的一票`;
                });

                let danmuLength = this.danmuList.length;
                this.danmuList = list;
                this.addBarrage(isInit || danmuLength === 0);
            } catch (e) {
                console.error("查询弹幕列表失败:", e);
                uni.showToast({
                    title: (e && e.message) || "查询弹幕列表失败",
                    icon: "none",
                    duration: 2000,
                });
            }
        },

        // 添加弹幕到组件
        addBarrage(isInit) {
            if (!isInit || !this.danmuList.length) {
                return;
            }

            const barrageComp = this.$refs && this.$refs.bulletComment;
            if (barrageComp) {
                barrageComp.getBarrageInstance({
                    duration: 15, // 弹幕动画时长
                    lineHeight: 1.8, // 弹幕行高，更紧凑
                    padding: [5, 5, 5, 5], // 弹幕区四周留白
                    alpha: 1, // 全局透明度
                    font: "10px PingFang SC", // 全局字体
                    range: [0, 1], // 弹幕显示的垂直范围
                    tunnelShow: false, // 不显示轨道线
                    tunnelMaxNum: 100, // 隧道最大缓冲长度
                    maxLength: 50, // 弹幕最大字节长度
                    safeGap: 10, // 发送时的安全间隔
                    enableTap: false, // 不允许点击弹幕
                    danmuList: this.danmuList,
                });
            }
        },

        // 初始化弹幕组件
        initBulletComment() {
            this.$nextTick(() => {
                // 获取弹幕数据并初始化
                this.getBarrageList(true);
            });
        },

        // 加载初始弹幕数据（模拟数据）
        loadInitialDanmu() {
            const initialDanmu = [];

            // 生成15条初始弹幕
            for (let i = 0; i < 15; i++) {
                initialDanmu.push({
                    content: "这是弹幕",
                    color: "#000000", // 黑色文字，不要图片
                });
            }

            // 添加到弹幕列表
            this.danmuList = [...initialDanmu];

            // 分批随机加载弹幕，增加随机性
            this.loadDanmuRandomly(initialDanmu);
        },

        // 发送弹幕
        sendBulletComment() {
            if (!this.bulletComment.trim()) {
                uni.showToast({
                    title: "请输入弹幕内容",
                    icon: "none",
                });
                return;
            }

            // 构造弹幕数据
            const newDanmu = {
                content: this.bulletComment.trim(),
                color: "#000000", // 黑色文字，不要图片
            };

            // 本地发送弹幕
            this.sendDanmuLocal(newDanmu);

            // 预留接口调用位置
            // this.sendDanmuToServer(newDanmu);

            // 清空输入框
            this.bulletComment = "";
        },

        // 本地发送弹幕
        sendDanmuLocal(danmuData) {
            try {
                // 添加到本地弹幕列表
                this.danmuList.push(danmuData);

                // 发送到弹幕组件
                if (this.$refs.bulletComment) {
                    this.$refs.bulletComment.addData([danmuData]);
                }
            } catch (error) {
                console.error("弹幕发送失败:", error);
                uni.showToast({
                    title: "弹幕发送失败",
                    icon: "none",
                });
            }
        },

        // 预留：发送弹幕到服务器
        async sendDanmuToServer(danmuData) {
            try {
                // TODO: 实现发送弹幕到服务器的逻辑
                console.log('发送弹幕到服务器:', danmuData);
            } catch (error) {
                console.error("发送弹幕到服务器失败:", error);
                // 可以在这里处理错误，比如显示错误提示
            }
        },

        // 弹幕重新加载回调
        async reloadDanmu(type) {
            const currentTime = Date.now();

            // 检查冷却时间，避免频繁重载
            if (currentTime - this.lastReloadTime < this.reloadCooldown) {
                return;
            }
            this.lastReloadTime = currentTime;

            const barrageComp = this.$refs && this.$refs.bulletComment;
            if (type === "addDanmu") {
                // 继续添加更多弹幕
                await this.getBarrageList(false);
                if (barrageComp) {
                    barrageComp.open();
                    barrageComp.addData(this.danmuList);
                }
            } else {
                // 重新初始化弹幕，加载完整的弹幕数据
                await this.getBarrageList(true);
            }
        },

        // 预留：从服务器获取弹幕数据
        async getDanmuFromServer() {
            try {
                // TODO: 调用后端接口获取弹幕
                const response = await uni.request({
                    url: "/api/game/danmu/list",
                    method: "GET",
                    data: {
                        gameId: this.gameDetail.id,
                        page: 1,
                        limit: 10,
                    },
                });

                if (response.data.code === 200) {
                    const danmuList = response.data.data || [];
                    if (this.$refs.bulletComment && danmuList.length > 0) {
                        this.$refs.bulletComment.addData(danmuList);
                    }
                }
            } catch (error) {
                console.error("获取弹幕数据失败:", error);
            }
        },

        // 获取随机颜色
        getRandomColor() {
            const colors = [
                "#FF6B6B",
                "#4ECDC4",
                "#45B7D1",
                "#96CEB4",
                "#FFEAA7",
                "#DDA0DD",
                "#98D8C8",
                "#F7DC6F",
                "#BB8FCE",
                "#85C1E9",
                "#F8C471",
                "#82E0AA",
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        },

        // ========== 调试方法 ==========

        // 调试：开始游戏
        debugStartGame() {
            this.gameDetail.status = 2;
            if (this.gameDetail.isRegistered) {
                this.gameDetail.userGameStatus = 'choosing';
                this.startChoiceCountdown();
            }
        },

        // 调试：模拟报名
        debugRegister() {
            this.gameDetail.isRegistered = true;
            this.gameDetail.totalPlayers++;
            this.gameDetail.remainingCount++;
        },

        // 调试：显示结果
        debugShowResult() {
            if (this.gameDetail.userChoice) {
                this.showRoundResult();
            } else {
                uni.showToast({
                    title: "请先选择红方或蓝方",
                    icon: "none"
                });
            }
        },

        // 调试：结束游戏
        debugEndGame() {
            this.gameDetail.status = 3;
            this.gameDetail.userGameStatus = 'winner';
            this.clearCountdown();

            // 生成模拟获奖数据
            this.generateMockWinners();
        },

        // 生成模拟获奖数据
        generateMockWinners() {
            const mockUsers = ['用户张三', '用户李四', '用户王五', '用户赵六', '用户钱七'];
            const currentTime = new Date();

            this.winnerList = [];
            for (let i = 0; i < this.gameDetail.winnerCount; i++) {
                const winTime = new Date(currentTime.getTime() - i * 1000);
                this.winnerList.push({
                    prizeName: "苹果16pro max 256GB*1",
                    userName: mockUsers[i % mockUsers.length],
                    winTime: this.formatDateTime(winTime),
                    prizeImage: `https://picsum.photos/96/96?random=${i + 1}`
                });
            }
        },

        // 格式化日期时间
        formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        // 调试：重置游戏
        debugReset() {
            this.gameDetail.status = 1;
            this.gameDetail.isRegistered = false;
            this.gameDetail.userGameStatus = 'waiting';
            this.gameDetail.userChoice = null;
            this.gameDetail.currentRound = 1;
            this.gameDetail.currentRoundData.showResult = false;
            this.clearCountdown();
            this.initCountdown();
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 24rpx 32rpx;
        padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
        .header {
            margin-top: 24rpx;
        }
        .prize {
            margin-top: 32rpx;
            .prize-item {
                margin-top: 24rpx;
                padding: 12rpx;
                border-radius: 12rpx;
                background: #f7f8fa;
            }
        }
        .game-info {
            border-radius: 8rpx;
            background: #1d7bf750;
            color: #fff;
            margin-top: 24rpx;
            .item {
                flex: 1;
                padding: 12rpx;

                &.active {
                    padding: 12rpx;
                    border-radius: 8rpx;
                    background: #1d7bf7;
                }
                .lable {
                    font-size: 24rpx;
                }
                .value {
                    font-size: 24rpx;
                }
            }
        }
        .game-status {
            margin-top: 40rpx;
        }
        .game-choice {
            margin-top: 24rpx;
            .left {
                height: 80rpx;
                border-radius: 16rpx 0 0 16rpx;
                background: linear-gradient(90deg, #f71914 0%, #f5582b 100%);
                transition: all 0.3s ease;
                cursor: pointer;

                &:active {
                    opacity: 0.8;
                    transform: scale(0.98);
                }
            }
            .right {
                height: 80rpx;
                border-radius: 0 16rpx 16rpx 0;
                background: linear-gradient(90deg, #2171fe 0%, #0435ff 100%);
                transition: all 0.3s ease;
                cursor: pointer;

                &:active {
                    opacity: 0.8;
                    transform: scale(0.98);
                }
            }
        }
        // 倒计时样式
        .countdown-container {
            margin-top: 22rpx;
            .countdown-time {
                .time-item {
                    background: #f50c0c;
                    border-radius: 8rpx;
                    padding: 8rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .time-num {
                        font-size: 24rpx;
                        font-weight: bold;
                        color: #fff;
                        line-height: 1;
                    }
                }

                .time-separator {
                    font-size: 24rpx;
                    font-weight: bold;
                    color: #f50c0c;
                    line-height: 1;
                }
            }
        }

        .btn {
            margin-top: 56rpx;
        }
        .bullet-comment {
            width: 100%;
            height: 350rpx;
            margin-top: 40rpx;
            .bullet-content {
                width: 100%;
                height: 350rpx;
                box-sizing: border-box;
            }
        }
        .send-message {
            margin-top: 40rpx;
        }

        // 获奖名单样式
        .winner-list {
            margin-top: 24rpx;
            background: #fff;
            border-radius: 16rpx;

            .winner-header {
                text-align: center;
                padding-bottom: 32rpx;
                border-bottom: 2rpx solid #f0f0f0;
            }

            .winner-title {
                padding: 24rpx 0 16rpx 0;
            }

            .winner-item {
                background: #f8f9fa;
                border-radius: 12rpx;
                padding: 20rpx;
                margin-bottom: 16rpx;
                transition: all 0.3s ease;

                &:last-child {
                    margin-bottom: 0;
                }

                .winner-info {
                    flex: 1;

                    .winner-prize {
                        font-weight: bold;
                        color: #070f1a;
                    }

                    .winner-user, .winner-time {
                        color: #606266;
                        font-size: 24rpx;
                    }
                }
            }
        }
    }
}
</style>
