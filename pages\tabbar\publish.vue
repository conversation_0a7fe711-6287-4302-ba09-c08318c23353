<template>
    <view class="page">
        <section class="nav-bar">
            <c-navBar title="发布帖子" isBack isShadow isPerch></c-navBar>
        </section>
        <section class="content">
            <view class="post">
                <text class="mf-font-32 mf-weight-bold" style="color: #191919">帖子内容</text>
                <u-textarea
                    height="240rpx"
                    :maxlength="planList[currentPlan].size"
                    border="none"
                    v-model="post"
                    placeholder="请输入内容"
                    count
                ></u-textarea>
            </view>
            <view class="plan">
                <view
                    class="plan-item flex flex-col flex-center"
                    :class="currentPlan === index ? 'active' : ''"
                    v-for="(item, index) in planList"
                    :key="index"
                    @click="selectPlan(index)"
                    :style="{ background: index === 0 ? '#f7f7f7' : index === 1 ? '#F9F5EC' : '#F3F6E3' }"
                >
                    <text class="mf-font-28" style="color: #191919">{{ item.title }}</text>
                    <view class="price mf-font-20" style="color: #f50c0c; height: 60rpx; width: 100%; text-align: center">
                        <text v-if="item.price">￥{{ item.price }}</text>
                    </view>
                    <text class="mf-font-24 desc-text" style="color: #666666">{{ item.desc }}</text>
                </view>
            </view>
            <view class="illustrate flex flex-col flex-center" style="margin-top: 48rpx">
                <text class="mf-font-28 mf-weight-bold" style="color: #272b33">发帖说明</text>
                <view style="margin-top: 24rpx">
                    <u-parse :content="content"></u-parse>
                </view>
            </view>
            <view class="upload-image flex flex-col gap-24" v-if="currentPlan !== 0">
                <text class="mf-font-32 mf-weight-bold" style="color: #191919">上传图片(限{{ planList[currentPlan].imgNum }}张)</text>
                <c-upLoadImgs @load="load" :maxCount="planList[currentPlan].imgNum"></c-upLoadImgs>
            </view>
        </section>
        <section class="footer">
            <!-- 免费发布按钮 -->
            <u-button
                v-if="currentPlan === 0"
                type="primary"
                size="large"
                :custom-style="{
                    backgroundColor: '#1D7BF7',
                    borderColor: '#1D7BF7',
                    borderRadius: '36rpx',
                    height: '72rpx',
                }"
                @click="publishPost"
            >
                <view class="flex align-center gap-12">
                    <text class="mf-font-36" style="color: #fff">发布</text>
                </view>
            </u-button>

            <!-- 付费发布按钮 -->
            <view v-else class="pay-footer flex align-center justify-between">
                <view class="price-info flex align-center gap-8">
                    <text class="mf-font-36" style="color: #666;">需付:</text>
                    <text class="mf-font-36 mf-weight-bold" style="color: #f50c0c;">￥{{ planList[currentPlan].price }}</text>
                </view>
                <u-button
                    type="primary"
                    size="large"
                    :custom-style="{
                        backgroundColor: '#1D7BF7',
                        borderColor: '#1D7BF7',
                        borderRadius: '36rpx',
                        height: '72rpx',
                        width: '272rpx',
                    }"
                    @click="payAndPublish"
                >
                    <view class="flex align-center gap-12">
                        <text class="mf-font-28" style="color: #fff">立即支付</text>
                    </view>
                </u-button>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentPlan: 0, // 当前选中的发布方案，默认选中免费发布
            post: "", // 帖子内容
            planList: [
                { title: "免费发布", price: null, size: 20, desc: "可发布20字\n以内的内容", imgNum: 0 },
                { title: "普通发布", price: "9.98", size: 100, desc: "可发布100字以内\n的内容+3张图片", imgNum: 3 },
                { title: "高级发布", price: "9.98", size: 300, desc: "可发布300字以内\n的内容+6张图片", imgNum: 6 },
            ],
            content:
                '<p style="font-size: 15px; color: #191919;">发帖说明发帖说明发帖说明发帖说明发帖说明发帖说明发帖说明发帖说明发帖说明发帖说明发帖说明发帖说明发帖说明</p>', // 发帖说明
        };
    },
    onShow() {},
    methods: {
        handleTabChange() {
            // this.currentTab = index;
        },

        // 选择发布方案
        selectPlan(index) {
            this.currentPlan = index;
            console.log("选择了方案:", this.planList[index].title);
        },

        // 免费发布
        publishPost() {
            if (!this.post.trim()) {
                uni.showToast({
                    title: '请输入帖子内容',
                    icon: 'none'
                });
                return;
            }

            if (this.post.length > this.planList[this.currentPlan].size) {
                uni.showToast({
                    title: `内容不能超过${this.planList[this.currentPlan].size}字`,
                    icon: 'none'
                });
                return;
            }

            // 执行发布逻辑
            console.log('免费发布帖子:', this.post);
            uni.showToast({
                title: '发布成功',
                icon: 'success'
            });
        },

        // 付费发布
        payAndPublish() {
            if (!this.post.trim()) {
                uni.showToast({
                    title: '请输入帖子内容',
                    icon: 'none'
                });
                return;
            }

            if (this.post.length > this.planList[this.currentPlan].size) {
                uni.showToast({
                    title: `内容不能超过${this.planList[this.currentPlan].size}字`,
                    icon: 'none'
                });
                return;
            }

            // 执行支付逻辑
            console.log('付费发布帖子:', {
                content: this.post,
                plan: this.planList[this.currentPlan],
                price: this.planList[this.currentPlan].price
            });

            uni.showModal({
                title: '确认支付',
                content: `确认支付￥${this.planList[this.currentPlan].price}发布帖子？`,
                success: (res) => {
                    if (res.confirm) {
                        // 这里调用支付接口
                        uni.showToast({
                            title: '支付成功，发布完成',
                            icon: 'success'
                        });
                    }
                }
            });
        },

        // 图片上传
        load(e) {
            console.log(e);
        },
    },
};
</script>

<style lang="scss" scoped>
.page {
    min-height: calc(100vh - 120rpx);
    background: #fff;
    padding-bottom: 120rpx; // 为 tabbar 预留空间
    .content {
        padding: 40rpx 32rpx;
        .post {
            ::v-deep .u-textarea {
                margin-top: 20rpx;
                background: #f7f7f7;
            }

            ::v-deep .u-textarea__count {
                background: transparent !important;
            }
        }
        .plan {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: 18rpx;
            margin-top: 40rpx;
            .plan-item {
                border-radius: 12rpx;
                padding: 28rpx 0;
                position: relative;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 2rpx solid transparent;

                &.active {
                    border: 2rpx solid #1d7bf7;
                    position: relative;
                    background: #d2e4fd !important;
                    // 右上角的选中标识
                    &::after {
                        content: "";
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 0;
                        height: 0;
                        border-style: solid;
                        border-width: 0 48rpx 48rpx 0;
                        border-color: transparent #1d7bf7 transparent transparent;
                    }

                    // 右上角的对勾图标
                    &::before {
                        content: "✓";
                        position: absolute;
                        top: 0rpx;
                        right: 6rpx;
                        color: #fff;
                        font-size: 20rpx;
                        font-weight: bold;
                        z-index: 1;
                    }
                }

                .desc-text {
                    text-align: center;
                    line-height: 1;
                    white-space: pre-line; // 支持换行符显示
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2; // 限制显示2行
                    line-clamp: 2; // 标准属性
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 40rpx;
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 999;
        // background: #fff;
        // border-top: 2rpx solid #f0f0f0;

        .pay-footer {
            width: 100%;

            .price-info {
                flex: 1;

                .mf-font-32 {
                    font-size: 32rpx;
                }
            }
        }
    }
}
</style>
