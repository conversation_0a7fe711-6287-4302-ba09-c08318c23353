<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="搜索" isBack isPerch isShadow></c-navBar>
        </section>
        <section class="content">
            <view class="search-bar flex align-center gap-10">
                <view class="leftbox flex align-center gap-10" @click="show = true">
                    <text class="mf-font-24" style="color: #070f1a">{{ currentArea }}</text>
                    <u-icon name="arrow-down-fill" color="#070F1A" size="10"></u-icon>
                </view>
                <u-search
                    placeholder="请输入用户名称或帖子内容"
                    v-model="keyword"
                    shape="square"
                    bgColor="#F5F6F7"
                    :actionStyle="{
                        borderRadius: '8rpx',
                        background: '#1D7BF7',
                        color: '#fff',
                        height: '68rpx',
                        lineHeight: '68rpx',
                    }"
                    animation
                    clearabled
                    showAction
                    @search="handleSearch"
                    @custom="handleSearch"
                ></u-search>
            </view>
            <view class="search-history" v-if="resultsList.length === 0">
                <view class="header flex align-center justify-between">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">搜素历史</text>
                    <u-icon name="trash" color="#666" size="24rpx" label="清空" labelSize="24rpx" :top="1"></u-icon>
                </view>
                <view class="list flex flex-wrap gap-20">
                    <view class="item" v-for="(item, index) in historyList" :key="index" @click="handleSearch(item)">
                        <text class="mf-font-28" style="color: #666">{{ item }}</text>
                    </view>
                </view>
            </view>
            <scroll-view v-if="resultsList.length > 0" class="results-list" scroll-y>
                <view class="item" v-for="(item, index) in resultsList" :key="index">
                    <view class="header flex align-center justify-between">
                        <view class="left flex align-center gap-24">
                            <u-image :src="item.avatar" width="80rpx" height="80rpx" radius="8rpx" mode="aspectFill"></u-image>
                            <view class="flex flex-col gap-16">
                                <view class="flex align-center gap-16">
                                    <text class="mf-font-28 mf-weight-bold" style="color: #191919">{{ item.title }}</text>
                                    <text class="tag mf-font-20" style="color: #fff" v-if="item.tag">{{ item.tag }}</text>
                                </view>
                                <text class="mf-font-20" style="color: #909399">1小时前</text>
                            </view>
                        </view>
                        <view class="right">
                            <view class="btn flex align-center gap-8">
                                <u-image src="/static/common/msg.png" width="24rpx" height="24rpx" mode="aspectFill"></u-image>
                                <text class="mf-font-20" style="color: #fff">私聊</text>
                            </view>
                        </view>
                    </view>
                    <view class="body">
                        <text class="message mf-font-24 u-line-2" style="color: #666">
                            {{ item.description }}
                        </text>
                        <view class="images" v-if="item.images && item.images.length > 0">
                            <u-image
                                v-for="(img, imgIndex) in item.images.slice(0, 3)"
                                :key="imgIndex"
                                :src="img"
                                width="220rpx"
                                height="220rpx"
                                radius="12rpx"
                                mode="aspectFill"
                            ></u-image>
                            <view v-if="item.images.length > 3" class="more flex align-center justify-center">
                                <text class="mf-font-20" style="color: #fff">共{{ item.images.length }}张</text>
                            </view>
                        </view>
                        <view class="location flex align-center justify-between">
                            <view class="flex align-center gap-12">
                                <u-icon name="map" bold size="20rpx" color="#999"></u-icon>
                                <text class="mf-font-20" style="color: #999">{{ item.address }}</text>
                            </view>
                            <text class="mf-font-20" style="color: #1d7bf7">距你3km</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
        <u-picker
            :show="show"
            close-on-click-overlay
            :columns="[columns]"
            @cancel="show = false"
            @close="show = false"
            @change="changeHandler"
            @confirm="confirmHandler"
        >
        </u-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            show: false,
            columns: ["四川一区", "四川二区", "南方大区"],
            currentArea: "四川一区",
            keyword: "", // 搜索关键词
            historyList: [
                "美食推荐",
                "二手电脑",
                "求租房子",
                "兼职招聘信息",
                "宠物寄养",
                "学习资料分享",
                "跑腿代购",
                "汽车保养维修",
                "健身教练推荐",
                "手机维修",
            ],
            resultsList: [],
        };
    },
    onLoad(options) {},
    methods: {
        // 选择地区
        changeHandler(e) {
            this.currentArea = e.value[0];
        },
        // 确认选择
        confirmHandler(e) {
            this.currentArea = e.value[0];
            this.show = false;
        },
        // 搜索
        handleSearch(keyword = "") {
            this.keyword = keyword;
            this.resultsList = [
                {
                    avatar: "https://picsum.photos/750/750?random=1",
                    title: "用户名称",
                    tag: "标签",
                    description: "帖子内容",
                    images: [
                        "https://picsum.photos/750/750?random=1",
                        "https://picsum.photos/750/750?random=2",
                        "https://picsum.photos/750/750?random=3",
                        "https://picsum.photos/750/750?random=4",
                    ],
                    address: "四川省成都市武汉区科创园区街105号",
                },
            ];
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 24rpx 32rpx;
        .search-bar {
            background: #fff;
            overflow: hidden;

            ::v-deep .u-search__action--active {
                width: 120rpx;
            }
        }
        .search-history {
            padding: 40rpx 0 24rpx 0;
            .list {
                margin-top: 24rpx;
                .item {
                    padding: 18rpx 28rpx;
                    background: #fafafa;
                    border-radius: 8rpx;
                }
            }
        }
        .results-list {
            height: calc(100vh - 384rpx);
            margin-top: 32rpx;
            .item {
                margin-bottom: 8rpx;
                background: #fff;
                &:last-child {
                    margin-bottom: 0;
                }
                .header {
                    .left {
                        .tag {
                            background: #ff8e0d;
                            padding: 6rpx 10rpx;
                            border-radius: 4rpx;
                        }
                    }
                    .right {
                        .btn {
                            padding: 12rpx 16rpx;
                            border-radius: 24rpx;
                            background: #1d7bf7;
                        }
                    }
                }
                .body {
                    .message {
                        margin-top: 24rpx;
                    }
                    .images {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        grid-gap: 12rpx;
                        margin-top: 24rpx;
                        position: relative;
                        .more {
                            position: absolute;
                            right: 8rpx;
                            bottom: 8rpx;
                            width: 72rpx;
                            height: 32rpx;
                            background: rgba(0, 0, 0, 0.5);
                            border-radius: 20rpx;
                        }
                    }

                    .location {
                        margin-top: 24rpx;
                    }
                }
            }
        }
    }
}
</style>
