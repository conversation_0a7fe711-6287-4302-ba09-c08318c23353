<template>
	<!-- 登录 -->
	<view>
		<view class="tac">
			<image class="logo" src="/static/common/logo.png" mode=""></image>
			<view class="tips">欢迎登陆</view>
			<!-- 微信一键登录 -->
			<view class="btn-box">
				<button v-if="!isAgree" class="wx-login" @click="notAgreed">一键登录</button>
				<button v-else-if="needWechatPhone" open-type="getPhoneNumber" class="wx-login"
					@getphonenumber="wechatLogin">一键登录</button>
				<button v-else class="wx-login" @click="loginSuccess(resData)">一键登录</button>
			</view>
			<view class="agreement-box">
				<view class="ag-box" @click="tabAgree">
					<image v-if="isAgree" src="/static/common/select.png" mode=""></image>
					<image v-else src="/static/common/noSelect.png" mode=""></image>
				</view>
				<view class="agreement">
					<text @click.stop="tabAgree">我已阅读并同意</text>
					<text class="primary-color" @click.stop="jumpXy(1)">《用户协议》</text>
					<text class="primary-color" @click.stop="jumpXy(2)">《隐私协议》</text>
				</view>
			</view>
		</view>
		<u-popup :show="showLogin" @close="this.showLogin = false" mode='bottom' :round='20' :closeable='true'>
			<view class="popu">
				<view class="popu-top">
					登录信息
				</view>
				<view class="popu-main">
					<button class="u-reset-button avatar" type="balanced" open-type="chooseAvatar"
						@chooseavatar="onChooseAvatar">
						<view class="popu-box">
							<view class="left">
								头像
							</view>
							<view class="right">
								<image class="avatar" :src="vuex_imgUrl + formData.avatarUrl">
								</image>
							</view>
						</view>
					</button>
					<view class="popu-box">
						<view class="left">
							昵称
						</view>
						<view class="right">
							<input type="nickname" class="weui-input" placeholder="请输入昵称" v-model="formData.nickName"
								@change="getNickname" />
						</view>
					</view>
				</view>
				<!-- <button class="popu-footer" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">
					授权手机号并登录
				</button> -->
				<view class="popu-footer" @click="creatLoginC">
					登录
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			tipsShow: false,
			resData: {},
			isAgree: false, //是否同意协议
			needWechatPhone: true, //是否需要微信手机号授权,
			formData: {
				phone: '',
				avatarUrl: '',
				nickName: '',
				phoneCode: ''
			},
			showLogin: false,
		};
	},
	onLoad() {
		// this.creatLogin();
	},
	methods: {
		// 跳转协议
		jumpXy(e) {
			uni.navigateTo({
				url: '/pages/person/userPrivacy?type=' + e
			})
		},
		// 同意/不同意协议
		tabAgree() {
			this.isAgree = !this.isAgree;
		},
		onChooseAvatar(e) {
			let that = this
			console.log(e.detail.avatarUrl, "e");

			uni.uploadFile({
				url: that.vuex_baseUrl + '/common/upload',
				filePath: e.detail.avatarUrl,
				name: 'file',
				formData: {
					user: 'test'
				},
				success: (res) => {
					console.log(JSON.parse(res.data).fileName, "res")
					that.formData.avatarUrl = JSON.parse(res.data).fileName
				}
			});
		},
		// 初始登录获取openid
		creatLogin() {
			uni.showLoading({
				title: '登录中...',
				mask: true
			})

			uni.login({
				provider: "weixin", //使用微信登录
				success: (loginRes) => {
					console.log('sss', this.formData.avatarUrl);
					uni.getUserInfo({
						success: async (userRes) => {
							try {
								let res = await this.$api.userLogin({
									loginCode: loginRes.code,
									avatar: this.formData.avatarUrl,
									nickname: this.formData.nickName,
									phone: this.formData.phoneCode,
								}, {
									custom: {
										catch: true,
										toast: false
									}
								})
								this.resData = res.data
								if (res.code == 200) {
									this.needWechatPhone = res.data.needWechatPhone
									this.loginSuccess(res)
								}
								uni.hideLoading()
							} catch (e) {
								//TODO handle the exception
								uni.hideLoading()

								console.log('e', e);
								if (e.code == 500) {
									uni.showToast({
										title: e.msg,
										icon: 'none',
										duration: 2000
									})
								} else {
									this.tipsShow = true
								}
							}
						},
						fail: (err) => {
							console.log('获取用户信息失败:', err);
						}
					});
				},
			});
		},
		creatLoginC() {
			if (!this.formData.avatarUrl) {
				return uni.showToast({
					title: '请选择头像',
					icon: 'none'
				})
			}
			if (!this.formData.nickName) {
				return uni.showToast({
					title: '请输入昵称',
					icon: 'none'
				})
			}
			this.creatLogin()
		},
		// 未同意协议
		notAgreed() {
			uni.showToast({
				title: '请先同意用户协议',
				icon: 'none',
				duration: 2000,
			})
		},
		// 微信一键登录
		async wechatLogin(e) {
			if (!this.isAgree) {
				return uni.showToast({
					title: "请先查看并同意协议",
					icon: "none",
					duration: 2000,
				});
			}

			if (e.detail.errMsg == "getPhoneNumber:fail user deny") {
				uni.showToast({
					title: "用户拒绝授权",
					icon: "none",
					duration: 2000,
				});
				return;
			}
			console.log('e', e);
			this.formData.phoneCode = e.detail.code
			this.$api.getPhone({ code: e.detail.code }).then(res => {
				console.log('phone', res);
				this.formData.phoneCode = res.data.phone
				console.log(this.formData ,'888888888');
				
			})
			this.firstLogin()


		},
		// 登录成功
		async loginSuccess(res) {

			if (res.code == 200) {
				uni.showToast({
					title: '登录成功！',
					icon: 'none',
					duration: 2000
				});
				this.$u.vuex('vuex_token', res.data.accessToken);
				uni.setStorageSync('token', res.data.accessToken);
				uni.setStorageSync('userInfo', res.data.detail);

				let url = '/pages/tabbar/home'
				setTimeout(() => {
					uni.reLaunch({
						url: url
					})
				}, 500)
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none',
					duration: 2000
				});
			}
		},
		firstLogin() {
			const that = this
			uni.login({
				provider: 'weixin', //使用微信登录
				success(loginRes) {
					that.$api.loginFirst({
						loginCode: loginRes.code,
						phoneCode: that.formData.phoneCode,
					}).then(res => {
						console.log('res', res);
						if (res.data == "false") {
							that.showLogin = true
						} else {
							that.creatLogin()

						}
					})
				},
			})

		},
		getNickname(e) {
			this.formData.nickName = e.detail.value;
		},
	}
}
</script>

<style lang="scss" scoped>
.tac {
	text-align: center;
font-family: "Bold", Source Han Serif SC;
}

.btn-box {
	display: flex;
	justify-content: center;
}

.logo {
	margin-top: 280rpx;
	text-align: center;
	width: 320rpx;
	height: 320rpx;
	// background: #0840AD;

}

.title {
	margin-top: 56rpx;
	font-size: 48rpx;
	font-weight: bold;
	color: #020D1A;
}

.tips {
	font-size: 40rpx;
	// color: #4591FE;
	margin-top: 80rpx;
	
}

.wx-login {
	margin-top: 120rpx;
	width: 480rpx;
	height: 96rpx;
	background: #0840AD;
	border-radius: 48rpx;
	font-size: 36rpx;
	color: #FFFFFF;
	display: flex;
	justify-content: center;
	align-items: center;

	.weixin {
		width: 48rpx;
		height: 46rpx;
		margin-right: 12rpx;
	}
}

.ple-login {
	padding: 40rpx 0rpx 85rpx;
	text-align: left;

	.inp-box {
		padding: 60rpx 0rpx 30rpx;
		margin: 0 70rpx;
		border-bottom: 1rpx solid #D8DEE6;
		position: relative;

		.phe-icon {
			position: absolute;
			top: 66rpx;
			left: 0;
			width: 28rpx;
			height: 28rpx;
		}

		input {
			padding-left: 60rpx;
		}

	}

	.login {
		margin-top: 100rpx;
		width: 640rpx;
		height: 96rpx;
		background: #04C15F;
		border-radius: 20rpx;
		color: #fff;
		font-size: 36rpx;
		font-weight: bold;
		color: #FFFFFF;

		&.unsatisfied {
			background: #E3E3E3;
			color: #9DA0A5;
		}
	}

	.tab {
		margin-bottom: 75rpx;
	}
}

.tab {
	margin-top: 40rpx;
	margin-bottom: 500rpx;
	text-align: center;
	font-size: 28rpx;
	color: #888990;
}

.agreement-box {
	position: fixed;
	bottom: 10%;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;

	.ag-box {
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	image {
		width: 24rpx;
		height: 24rpx;
	}

	.agreement {
		font-size: 28rpx;
		font-weight: 500;
		color: #666666;

		.primary-color {
			color: #0840AD;
		}
	}
}

.popu {
	.popu-top {
		padding: 30rpx;
		color: #353B55;
		font-size: 32rpx;
		font-weight: 700;
	}

	.popu-title {
		padding: 30rpx;
		background: #fafafa;
		color: #8f8f8f;
		font-size: 28rpx;
	}

	.popu-main {
		.popu-box {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #D7D9DB;
			color: #353B55;
			font-size: 32rpx;
			font-weight: 700;

			.avatar {
				background: #D7D9DB;

				width: 80rpx;
				height: 80rpx;
				border-radius: 80rpx;

			}

			.weui-input {
				text-align: right;
			}
		}
	}

	.popu-footer {
		width: 690rpx;
		height: 88rpx;
		background: #54BCBD;
		border: none;
		border-radius: 20rpx;
		line-height: 88rpx;
		text-align: center;
		color: #FFFFFF;
		font-weight: 700;
		font-size: 32rpx;
		margin: 40rpx auto;

	}
}
</style>