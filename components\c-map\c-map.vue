<template>
    <view class="map-content">
        <map
            id="map"
            :style="mapStyle"
            :latitude="latitude"
            :longitude="longitude"
            :scale="scale"
            :markers="markers"
            :show-location="showLocation"
            @markertap="onMarkerTap"
            @callouttap="onCalloutTap"
        >
            <cover-view slot="callout">
                <!-- 自定义气泡 -->
                <cover-view v-for="(bubble, index) in markers" :key="index">
                    <cover-view :marker-id="bubble.id">
                        <cover-view class="callout-content">
                            <cover-image class="callout-avatar" :src="bubble.avatar"></cover-image>
                            <cover-view class="callout-info flex-1">
                                <cover-view class="callout-title flex align-center">
                                    <cover-view
                                        class="callout-title__text mf-font-28 mf-weight-bold u-line-1"
                                        style="max-width: 180rpx"
                                    >
                                        {{ bubble.title }}
                                    </cover-view>
                                    <cover-view v-if="bubble.tag" class="callout-tag mf-font-20">{{ bubble.tag }}</cover-view>
                                </cover-view>
                                <cover-view class="callout-desc mf-font-20" style="max-width: 180rpx">
                                    {{ bubble.description }}
                                </cover-view>
                            </cover-view>
                        </cover-view>
                        <cover-view class="callout-arrow">▼</cover-view>
                    </cover-view>
                </cover-view>
            </cover-view>
        </map>
    </view>
</template>

<script>
export default {
    name: 'CMap',
    props: {
        // 地图中心纬度
        latitude: {
            type: Number,
            default: 30.6586
        },
        // 地图中心经度
        longitude: {
            type: Number,
            default: 104.0647
        },
        // 地图缩放级别
        scale: {
            type: Number,
            default: 16
        },
        // 标记点数据
        markers: {
            type: Array,
            default: () => []
        },
        // 是否显示当前位置
        showLocation: {
            type: Boolean,
            default: true
        },
        // 地图样式
        mapStyle: {
            type: String,
            default: 'width: 100%; height: calc(100vh - 384rpx)'
        }
    },
    methods: {
        // 气泡点击事件（使用地图的 callouttap 事件）
        onCalloutTap(e) {
            this.$emit('bubble-tap', e);
        },
        // 地图标记点击事件
        onMarkerTap(e) {
            this.$emit('marker-tap', e);
        }
    }
};
</script>

<style lang="scss" scoped>
.map-content {
    position: relative;
    width: 100%;
    height: 100%;
    
    .callout-content {
        background: #1d7bf7;
        border-radius: 16rpx;
        padding: 10rpx;
        width: 320rpx;
        max-height: 100rpx;
        box-shadow: 0 6rpx 20rpx rgba(29, 123, 247, 0.4);
        display: flex;
        align-items: center;
        gap: 20rpx;
        
        .callout-avatar {
            display: block;
            width: 80rpx;
            height: 80rpx;
            border-radius: 8rpx;
            background: #fff;
            margin-right: 16rpx;
        }

        .callout-info {
            color: #fff;
            
            .callout-title {
                .callout-title__text {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .callout-tag {
                    background: #ff8e0d;
                    padding: 6rpx 10rpx;
                    border-radius: 4rpx;
                    line-height: -1;
                    margin-left: 12rpx;
                }
            }
            
            .callout-desc {
                margin-top: 15rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .callout-arrow {
        color: #1d7bf7;
        font-size: 32rpx;
        text-align: center;
        line-height: 1;
        margin: 0 auto;
        margin-top: -10rpx;
        width: 40rpx;
        height: 32rpx;
    }
}
</style>
