<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="领取奖品" isShadow isBack isPerch> </c-navBar>
        </section>
        <section class="content">
            <!-- 奖品信息 -->
            <view class="prize flex align-center gap-20">
                <u-image src="https://picsum.photos/96/96?random=1" radius="6rpx" width="96rpx" height="96rpx" mode="aspectFill"></u-image>
                <view class="flex flex-col gap-20">
                    <text class="prize-title mf-font-28" style="color: #070f1a">苹果16pro max 256GB*1</text>
                    <text class="prize-desc mf-font-20" style="color: #606266">礼品数量：3台</text>
                </view>
            </view>

            <!-- 表单区域 -->
            <view class="form-container">
                <u-form
                    :model="form"
                    ref="form"
                    :labelStyle="{
                        fontSize: '28rpx',
                        color: '#191919',
                    }"
                    :rules="rules"
                    label-position="left"
                    label-width="140"
                >
                    <!-- 姓名 -->
                    <u-form-item label="姓名" prop="name" class="form-item" borderBottom rightIcon="arrow-right">
                        <u-input
                            v-model="form.name"
                            placeholder="请输入"
                            inputAlign="right"
                            border="none"
                            suffixIcon="arrow-right"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            :customStyle="{
                                padding: '6rpx 0',
                            }"
                        />
                    </u-form-item>

                    <!-- 联系电话 -->
                    <u-form-item label="联系电话" prop="phone" class="form-item" borderBottom rightIcon="arrow-right">
                        <u-input
                            v-model="form.phone"
                            placeholder="请输入"
                            inputAlign="right"
                            border="none"
                            type="number"
                            suffixIcon="arrow-right"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            :customStyle="{
                                padding: '6rpx 0',
                            }"
                        />
                    </u-form-item>

                    <!-- 所在地区 -->
                    <u-form-item
                        label="所在地区"
                        prop="region"
                        class="form-item"
                        borderBottom
                        rightIcon="arrow-right"
                        @click="handleRegionConfirm"
                    >
                        <u-input
                            disabled
                            disabled-color="transparent"
                            :value="form.region"
                            placeholder="请选择"
                            inputAlign="right"
                            border="none"
                            suffixIcon="arrow-right"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            :customStyle="{
                                padding: '6rpx 0',
                            }"
                        />
                    </u-form-item>

                    <!-- 详细地址 -->
                    <u-form-item label="详细地址" prop="address" class="form-item" borderBottom rightIcon="arrow-right">
                        <u-input
                            v-model="form.address"
                            placeholder="请输入详细地址"
                            inputAlign="right"
                            border="none"
                            suffixIcon="arrow-right"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            :customStyle="{
                                padding: '6rpx 0',
                            }"
                        />
                    </u-form-item>
                </u-form>

                <!-- 确认领取按钮 -->
                <view class="submit-btn">
                    <u-button
                        type="primary"
                        :custom-style="{
                            backgroundColor: '#1D7BF7',
                            borderRadius: '58rpx',
                            height: '72rpx',
                            fontSize: '24rpx',
                        }"
                        @click="handleSubmit"
                    >
                        确认领取
                    </u-button>
                </view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            // 表单数据
            form: {
                name: "",
                phone: "",
                region: "",
                address: "",
            },
            // 表单验证规则
            rules: {
                name: [
                    {
                        required: true,
                        type: "string",
                        message: "请输入姓名",
                        trigger: ["blur", "change"],
                    },
                ],
                phone: [
                    {
                        required: true,
                        type: "string",
                        message: "请输入手机号",
                        trigger: ["blur", "change"],
                    },
                    {
                        pattern: /^1[3-9]\d{9}$/,
                        message: "请输入正确的手机号",
                        trigger: ["blur", "change"],
                    },
                ],
                region: [
                    {
                        required: true,
                        type: "string",
                        message: "请选择所在地区",
                        trigger: ["blur", "change"],
                    },
                ],
                address: [
                    {
                        required: true,
                        type: "string",
                        message: "请输入详细地址",
                        trigger: ["blur", "change"],
                    },
                ],
            },
        };
    },
    
    methods: {
        // 地区选择确认
        async handleRegionConfirm() {
            try {
                const res = await this.$fn.chooseLocation();
                console.log(res);
                if (res.errMsg == "chooseLocation:ok") {
                    this.form.region = res.address;
                    this.form.address = res.name;
                }else{
                    this.$fn.showToast("获取位置信息失败");
                }
            } catch (error) {
                console.log("位置选择异常：", error);
                this.$fn.showToast("获取位置信息失败");
            }
        },
        // 表单提交
        async handleSubmit() {
            try {
                // 表单验证
                const valid = await this.$refs.form.validate();
                if (!valid) {
                    return;
                }

                // 显示加载状态
                uni.showLoading({
                    title: "提交中...",
                });

                // 模拟提交请求
                setTimeout(() => {
                    uni.hideLoading();
                    uni.showToast({
                        title: "领取成功！",
                        icon: "success",
                    });

                    // 延迟返回上一页
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);
                }, 1000);
            } catch (error) {
                console.log("表单验证失败：", error);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fff;
    min-height: 100vh;

    .content {
        padding: 40rpx 32rpx;

        .prize {
            padding: 12rpx;
            border-radius: 12rpx;
            background: #f7f8fa;
        }

        .form-container {
            border-radius: 16rpx;

            .form-item {
                border-bottom: 2rpx solid #e6e6e6;
                padding: 32rpx 0;
            }
            .submit-btn {
                margin-top: 80rpx;
            }
        }
    }
}
</style>
